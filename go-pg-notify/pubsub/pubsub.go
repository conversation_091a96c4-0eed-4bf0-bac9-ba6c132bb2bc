package pubsub

import (
	"context"
	"demo/model"
	"encoding/json"
	"fmt"
	"log"
	"log/slog"
	"slices"
	"sync"
	"time"

	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/driver/pgdriver"
)

type PersistMessageFunc func(ctx context.Context, c PostgresPublisherConfig, tx bun.Tx, topic string, message *message.Message) (string, error)

type PostgresNotification struct {
	ID       string            `json:"id"`
	Payload  string            `json:"payload"`
	Metadata map[string]string `json:"metadata"`
}

type PostgresPublisherConfig struct {
	ConsumerGroup string
}

// PostgresPublisher publishes messages using PostgreSQL NOTIFY
type PostgresPublisher struct {
	db             *bun.DB
	ctx            context.Context
	persistMessage PersistMessageFunc
	config         PostgresPublisherConfig
}

// NewPostgresPublisher creates a new PostgreSQL publisher using Bun
func NewPostgresPublisher(db *bun.DB, config PostgresPublisherConfig) *PostgresPublisher {
	ctx := context.Background()
	return &PostgresPublisher{
		db:             db,
		ctx:            ctx,
		persistMessage: DefaultPersistMessage,
		config:         config,
	}
}

// Publish sends messages to the specified topic
func (p *PostgresPublisher) Publish(topic string, messages ...*message.Message) error {

	for _, msg := range messages {
		if err := p.publishSingle(p.ctx, topic, msg); err != nil {
			return err
		}
	}
	return nil
}

// Helper method to publish a single message
func (p *PostgresPublisher) publishSingle(ctx context.Context, topic string, msg *message.Message) error {
	tx, err := p.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	// Use a closure to ensure proper rollback handling
	rollback := func() {
		// Only rollback if not already committed
		if err := tx.Rollback(); err != nil {
			log.Printf("Failed to rollback transaction: %v", err)
		}
	}

	payload, err := p.persistMessage(ctx, p.config, tx, topic, msg)
	if err != nil {
		rollback()
		return fmt.Errorf("failed to create message record: %w", err)
	}

	// PostgreSQL NOTIFY has a limit of 8000 bytes for payload
	if len(payload) > 8000 {
		rollback()
		return fmt.Errorf("payload size exceeds PostgreSQL NOTIFY limit of 8000 bytes: %d bytes", len(payload))
	}

	if err := tx.Commit(); err != nil {
		rollback()
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	if err := pgdriver.Notify(ctx, p.db, topic, payload); err != nil {
		updateErr := p.UpdateMessageWithError(ctx, msg.UUID, err, model.StatusFailed)
		if updateErr != nil {
			return fmt.Errorf("Failed to publish message: %v", updateErr)
		}
		return fmt.Errorf("failed to publish message: %w", err)
	}

	msg.Ack()
	return nil
}

// UpdateMessageWithError updates a message with error information and status
func (p *PostgresPublisher) UpdateMessageWithError(ctx context.Context, messageID string, err error, status model.MessageStatus) error {
	msg := &model.Message{ID: messageID}

	updateQuery := p.db.NewUpdate().
		Model(msg).
		Set("status = ?", status).
		Set("updated_at = ?", time.Now().UTC()).
		Where("id = ?", messageID)

	if err != nil {
		updateQuery = updateQuery.Set("last_error = ?", err.Error())
	}

	res, updateErr := updateQuery.Exec(ctx)
	if updateErr != nil {
		return fmt.Errorf("failed to update message %s: %w", messageID, updateErr)
	}

	rowsAffected, rowsErr := res.RowsAffected()
	if rowsErr != nil {
		return fmt.Errorf("failed to get rows affected for message %s: %w", messageID, rowsErr)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("message %s not found or not updated", messageID)
	}

	return nil
}

// Close closes the publisher's database connection
func (p *PostgresPublisher) Close() error {
	return nil // No need to close anything since we're using the shared db connection
}

type PostgresSubscriberConfig struct {
	ConsumerGroup string
}

// PostgresSubscriber subscribes to messages using PostgreSQL LISTEN
type PostgresSubscriber struct {
	db          *bun.DB
	ln          *pgdriver.Listener
	channelSize int
	cancelFuncs []context.CancelFunc
	mu          sync.Mutex // To protect the cancelFuncs slice
	config      PostgresSubscriberConfig
}

// NewPostgresSubscriber creates a new PostgreSQL subscriber
func NewPostgresSubscriber(db *bun.DB, config PostgresSubscriberConfig) *PostgresSubscriber {

	return &PostgresSubscriber{
		db:          db,
		ln:          pgdriver.NewListener(db),
		channelSize: 1000,
		config:      config,
	}
}

// Subscribe listens for messages on the specified topic
func (s *PostgresSubscriber) Subscribe(ctx context.Context, topic string) (<-chan *message.Message, error) {
	// Create a new context that will be canceled when either the original context
	// is canceled or when Close() is called
	subCtx, cancel := context.WithCancel(ctx)

	if err := s.ln.Listen(ctx, topic); err != nil {
		cancel() // Clean up if we fail
		return nil, fmt.Errorf("failed to listen on topic %s: %w", topic, err)
	}

	msgChan := make(chan *message.Message, 1)

	// Store the cancel function to be called when Close() is called
	s.mu.Lock()
	s.cancelFuncs = append(s.cancelFuncs, cancel)
	s.mu.Unlock()

	buildMsg := func(n pgdriver.Notification) *message.Message {
		notification, err := postgresNotificationFromPayload(n.Payload)
		if err != nil {
			log.Printf("Failed to parse notification payload: %v", err)
			return nil
		}
		msg := message.NewMessage(notification.ID, []byte(notification.Payload))
		for key, value := range notification.Metadata {
			msg.Metadata.Set(key, value)
		}
		msg.Metadata.Set("topic", topic)
		msg.Metadata.Set("channel", n.Channel)
		return msg
	}

	notifCh := s.ln.Channel(
		pgdriver.WithChannelSize(s.channelSize),
		pgdriver.WithChannelOverflowHandler(func(n pgdriver.Notification) {
			slog.Error("Channel overflow", "topic", topic, "payload", n.Payload)
		}),
	)

	go func() {
		defer close(msgChan)

		for {
			select {
			case <-subCtx.Done():
				log.Println("Subscriber context canceled, stopping listener")
				return
			case n, ok := <-notifCh:
				if !ok {
					log.Println("Notification channel closed, stopping listener")
					return
				}

				msg := buildMsg(n)
				if msg == nil {
					continue
				}

				// Handle message claiming logic
				if s.config.ConsumerGroup == "" {
					log.Printf("Message claimed (no consumer group): %v", msg.UUID)
					select {
					case msgChan <- msg:
					case <-subCtx.Done():
						return
					}
					continue
				}

				claimed, err := s.claimMessage(ctx, msg)
				if err != nil {
					log.Printf("Failed to claim message: %v", err)
					continue
				}

				if claimed {
					log.Printf("Message already claimed: %v", msg.UUID)
					continue
				}

				log.Printf("Message claimed: %v", msg.UUID)
				slog.Info("Received message in subscriber", "topic", topic, "message", string(n.Payload))

				select {
				case msgChan <- msg:
				case <-subCtx.Done():
					return
				}
			}
		}
	}()

	return msgChan, nil
}

// Close closes the subscriber's connection and cancels all goroutines
func (s *PostgresSubscriber) Close() error {
	s.mu.Lock()
	// Reverse the slice in place
	slices.Reverse(s.cancelFuncs)
	for _, cancel := range s.cancelFuncs {
		cancel()
	}
	s.cancelFuncs = nil
	s.mu.Unlock()

	return s.ln.Close()
}

func (s *PostgresSubscriber) claimMessage(ctx context.Context, message *message.Message) (bool, error) {
	if s.config.ConsumerGroup == "" {
		return false, nil
	}

	// Create a proper model instance instead of a nil pointer
	msg := &model.Message{ID: message.UUID}

	lockedBy := s.config.ConsumerGroup
	updatedAt := time.Now().UTC()
	lockedUntil := time.Now().UTC().Add(30 * time.Second)

	// Use the model-based update approach
	res, err := s.db.NewUpdate().
		Model(msg).
		Set("updated_at = ?", updatedAt).
		Set("locked_by = ?", lockedBy).
		Set("locked_until = ?", lockedUntil).
		Set("locked_at = ?", updatedAt).
		Where("locked_at IS NULL").
		Where("id = ?", message.UUID).
		OmitZero().
		Exec(ctx)

	if err != nil {
		return false, fmt.Errorf("failed to claim message: %w", err)
	}

	numRows, err := res.RowsAffected()
	if err != nil {
		return false, fmt.Errorf("failed to get rows affected: %w", err)
	}

	return numRows == 0, nil // Return true if already claimed (no rows affected)
}
func DefaultPersistMessage(ctx context.Context, c PostgresPublisherConfig, tx bun.Tx, topic string, message *message.Message) (string, error) {
	payload := ""
	now := time.Now().UTC()
	msg := &model.Message{
		ID:          message.UUID,
		Topic:       topic,
		Payload:     []byte(message.Payload),
		Status:      model.StatusPending,
		Version:     1,
		Priority:    1,
		Metadata:    map[string]string{},
		CreatedAt:   now,
		UpdatedAt:   now,
		ScheduledAt: &now,
		Retries:     0,
		// LockedAt:    &now,
	}
	for key, value := range message.Metadata {
		msg.Metadata[key] = value
	}
	if _, err := tx.NewInsert().Model(msg).Returning("*").Exec(ctx); err != nil {
		log.Printf("Failed to queue message: %v", err)
		return payload, err
	}
	notification := PostgresNotification{
		ID:       msg.ID,
		Payload:  string(msg.Payload),
		Metadata: msg.Metadata,
	}
	jsonData, err := json.Marshal(notification)
	if err != nil {
		log.Printf("Failed to marshal notification: %v", err)
		return payload, err
	}
	payload = string(jsonData)
	return payload, nil
}

func postgresNotificationFromPayload(payload string) (*PostgresNotification, error) {
	var notification PostgresNotification
	if err := json.Unmarshal([]byte(payload), &notification); err != nil {
		return nil, err
	}
	return &notification, nil
}
