package model

import (
	"context"
	"fmt"
	"time"

	"github.com/uptrace/bun"
)

// MessageStatus represents the current status of a message
type MessageStatus string

const (
	StatusPending   MessageStatus = "pending"
	StatusScheduled MessageStatus = "scheduled"
	StatusRunning   MessageStatus = "running"
	StatusCompleted MessageStatus = "completed"
	StatusFailed    MessageStatus = "failed"
	StatusCanceled  MessageStatus = "canceled"
	StatusRetrying  MessageStatus = "retrying"
)

// Validate checks if the MessageStatus is valid
func (s MessageStatus) Validate() error {
	switch s {
	case StatusPending,
		StatusScheduled,
		StatusRunning,
		StatusCompleted,
		StatusFailed,
		StatusCanceled,
		StatusRetrying:
		return nil
	default:
		return fmt.Errorf("invalid message status: %s", s)
	}
}

// String returns the string representation of the MessageStatus
func (s MessageStatus) String() string {
	return string(s)
}

// Message represents a stored message/event/job in the database
type Message struct {
	bun.BaseModel `bun:"table:messages,alias:msg"`

	// Core fields
	ID       string            `bun:"id,pk,type:uuid"`
	Topic    string            `bun:"topic,notnull"`
	Payload  []byte            `bun:"payload,notnull"`
	Metadata map[string]string `bun:"metadata,type:jsonb"`

	// Event store fields
	EventType string `bun:"event_type"`
	Version   int    `bun:"version,notnull"`

	// Queue/Job fields
	Status   MessageStatus `bun:"status,notnull"`
	Priority int           `bun:"priority,notnull"`
	Retries  int           `bun:"retries,notnull"`

	// Timing fields
	CreatedAt   time.Time  `bun:"created_at,notnull,default:current_timestamp"`
	UpdatedAt   time.Time  `bun:"updated_at,notnull,default:current_timestamp"`
	ScheduledAt *time.Time `bun:"scheduled_at"`
	StartedAt   *time.Time `bun:"started_at"`
	FinishedAt  *time.Time `bun:"finished_at"`

	// Error tracking
	LastError  string `bun:"last_error"`
	ErrorCount int    `bun:"error_count,notnull"`

	// Locking/Processing
	LockedBy    *string    `bun:"locked_by"` // Worker ID
	LockedUntil *time.Time `bun:"locked_until"`
	LockedAt    *time.Time `bun:"locked_at"`

	// Tracking
	TraceID  string  `bun:"trace_id"`
	ParentID *string `bun:"parent_id,type:uuid"`
}

// BeforeAppendModel implements bun.BeforeAppendModelHook
func (m *Message) BeforeAppendModel(ctx context.Context, query bun.Query) error {
	switch query.(type) {
	case *bun.InsertQuery:
		m.CreatedAt = time.Now()
		m.UpdatedAt = m.CreatedAt
	case *bun.UpdateQuery:
		m.UpdatedAt = time.Now()
	}
	return nil
}
