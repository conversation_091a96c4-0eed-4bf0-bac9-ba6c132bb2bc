# https://taskfile.dev

version: '3'

tasks:
  debug-connect:
    desc: Connect to Delve debugger running in the container
    cmds:
      - dlv connect localhost:2345
    silent: true
    
  debug-vscode:
    desc: Generate VSCode launch.json configuration for remote debugging
    cmds:
      - |
        mkdir -p .vscode
        cat > .vscode/launch.json << 'EOF'
        {
          "version": "0.2.0",
          "configurations": [
            {
              "name": "Connect to Docker",
              "type": "go",
              "request": "attach",
              "mode": "remote",
              "remotePath": "/app",
              "port": 2345,
              "host": "127.0.0.1",
              "showLog": true
            }
          ]
        }
        EOF
      - echo "VSCode debug configuration created. Use the 'Run and Debug' panel to connect."
    silent: true
