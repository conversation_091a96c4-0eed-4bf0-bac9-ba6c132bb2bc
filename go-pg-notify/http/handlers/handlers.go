package handlers

import (
	"context"
	"demo/app"
	"demo/pubsub"
	"encoding/json"
	"fmt"
	"html/template"
	"log"
	"net/http"
	"path/filepath"
	"time"

	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/google/uuid"
)

type PublisherController struct {
	Topic      string
	publisher  *pubsub.PostgresPublisher
	subscriber *pubsub.PostgresSubscriber
	templates  *template.Template
	notiChan   <-chan *message.Message
}

type EventMessage struct {
	ID        string            `json:"id"`
	Payload   string            `json:"payload"`
	Metadata  map[string]string `json:"metadata"`
	Timestamp string            `json:"timestamp"`
}

type PageData struct {
	Topic string
}

func (pc *PublisherController) Publish() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		txtMsg := r.PathValue("message")

		log.Printf("Publishing message to topic: %s\n", pc.Topic)

		total := 30

		for i := 0; i < total; i++ {
			// Create a new Watermill message
			msg := message.NewMessage(
				uuid.NewString(),
				[]byte(txtMsg),
			)

			// Add some metadata
			msg.Metadata.Set("source", "postgres-notify")
			msg.Metadata.Set("send_at", time.Now().String())
			msg.Payload = []byte(fmt.Sprintf("Message %d: %s", i, txtMsg))
			err := pc.publisher.Publish(pc.Topic, msg)
			if err != nil {
				log.Printf("Failed to publish message: %v\n", err)
			}
			time.Sleep(100 * time.Millisecond)
		}

		log.Printf("Published message: %s\n", txtMsg)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("Message published successfully"))
	}
}

func (pc *PublisherController) StreamEvents(appCtx context.Context) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Set headers for Server-Sent Events
		w.Header().Set("Content-Type", "text/event-stream")
		w.Header().Set("Cache-Control", "no-cache")
		w.Header().Set("Connection", "keep-alive")
		w.Header().Set("Transfer-Encoding", "chunked")
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Headers", "Cache-Control")

		log.Printf("Client connected to event stream for topic: %s\n", pc.Topic)

		// Get flusher once and check if it's available
		flusher, ok := w.(http.Flusher)
		if !ok {
			http.Error(w, "Streaming unsupported", http.StatusInternalServerError)
			return
		}

		// Send initial connection message
		fmt.Fprintf(w, "data: {\"type\":\"connected\",\"message\":\"Connected to event stream\"}\n\n")
		flusher.Flush()

		// Create a merged context that cancels when either the request context or app context cancels
		ctx, cancel := context.WithCancel(r.Context())
		defer cancel()

		// Monitor app context in a separate goroutine
		go func() {
			select {
			case <-appCtx.Done():
				cancel() // Cancel our merged context when app context is done
			case <-ctx.Done():
				// Context already canceled, do nothing
			}
		}()

		// Stream messages
		for {
			select {
			case <-ctx.Done():
				// Send a final message to clients
				fmt.Fprintf(w, "data: {\"type\":\"shutdown\",\"message\":\"Server shutting down\"}\n\n")
				flusher.Flush()
				return
			case msg, ok := <-pc.notiChan:
				if !ok {
					log.Println("Message channel closed")
					return
				}

				// Convert metadata to map[string]string
				metadata := make(map[string]string)
				for key, value := range msg.Metadata {
					metadata[key] = value
				}

				// Create event message
				eventMsg := EventMessage{
					ID:        msg.UUID,
					Payload:   string(msg.Payload),
					Metadata:  metadata,
					Timestamp: time.Now().Format(time.RFC3339),
				}

				// Convert to JSON
				jsonData, err := json.Marshal(eventMsg)
				if err != nil {
					log.Printf("Failed to marshal event message: %v\n", err)
					continue
				}

				// Send as Server-Sent Event with proper formatting
				fmt.Fprintf(w, "data: %s\n\n", jsonData)
				flusher.Flush()

				// Acknowledge the message
				msg.Ack()

				log.Printf("Streamed message ID: %s - %s\n", msg.UUID, string(msg.Payload))

			}
		}
	}
}

func (pc *PublisherController) HomePage() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")

		data := PageData{
			Topic: pc.Topic,
		}

		err := pc.templates.ExecuteTemplate(w, "index.html", data)
		if err != nil {
			log.Printf("Error executing template: %v", err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			return
		}
	}
}

func RegisterHandlers(appCtx context.Context, app app.App, notiChan <-chan *message.Message) {
	// Load templates
	templates, err := template.ParseGlob(filepath.Join("templates", "*.html"))
	if err != nil {
		log.Fatalf("Failed to parse templates: %v", err)
	}

	pc := &PublisherController{
		Topic:      app.Config.Topic,
		publisher:  app.Publisher,
		subscriber: app.Subscriber, // Make sure this is available in your app struct
		templates:  templates,
		notiChan:   notiChan,
	}

	// Register routes
	app.Router.HandleFunc("/", pc.HomePage())
	app.Router.HandleFunc("/publish/{message}", pc.Publish())
	app.Router.HandleFunc("/events/stream", pc.StreamEvents(appCtx))
}
