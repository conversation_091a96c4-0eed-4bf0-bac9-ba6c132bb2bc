package server

import (
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"log"
	"net"
	"net/http"
	"time"

	"golang.org/x/net/http2"
)

type Options struct {
	Host         string
	Port         int
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
}

func DefaultOptions() *Options {
	// https://ieftimov.com/posts/make-resilient-golang-net-http-servers-using-timeouts-deadlines-context-cancellation/
	// https://labex.io/tutorials/go-how-to-optimize-http-timeouts-in-golang-435283

	return &Options{
		Host:        "0.0.0.0",
		Port:        8080,
		ReadTimeout: 15 * time.Second,
		// WriteTimeout:    15 * time.Second, // ❌ NEVER set this for SSE
	}
}

type OptionsFunc func(*Options)

// WithHost sets the host address
func WithHost(host string) OptionsFunc {
	return func(o *Options) {
		o.Host = host
	}
}

// WithPort sets the port number
func WithPort(port int) OptionsFunc {
	return func(o *Options) {
		o.Port = port
	}
}

// WithTimeouts sets all timeout values
func WithTimeouts(read, write time.Duration) OptionsFunc {
	return func(o *Options) {
		o.ReadTimeout = read
		o.WriteTimeout = write
	}
}

type Server struct {
	options *Options
	mux     *http.ServeMux
	server  *http.Server
	running bool
	ready   chan struct{}
}

func New(mux *http.ServeMux, opts ...OptionsFunc) *Server {
	options := DefaultOptions()

	for _, opt := range opts {
		opt(options)
	}

	return &Server{
		mux:     mux,
		options: options,
		ready:   make(chan struct{}, 1),
	}
}

func (s *Server) Start() error {
	if s.running {
		return errors.New("server is already running")
	}

	addr := fmt.Sprintf("%s:%d", s.options.Host, s.options.Port)

	s.server = &http.Server{
		Addr:         addr,
		Handler:      s.mux,
		ReadTimeout:  s.options.ReadTimeout,
		WriteTimeout: s.options.WriteTimeout,
		TLSConfig: &tls.Config{
			NextProtos: []string{"h2", "http/1.1"},
		},
	}

	// Enable HTTP/2
	if err := http2.ConfigureServer(s.server, &http2.Server{}); err != nil {
		log.Fatalf("Failed to configure HTTP/2: %v", err)
	}

	// Create listener before starting server
	listener, err := net.Listen("tcp", addr)
	if err != nil {
		return fmt.Errorf("failed to create listener: %w", err)
	}

	s.running = true
	log.Printf("Starting HTTP server on %s", addr)

	// Signal that the server is ready to accept connections
	close(s.ready)
	log.Printf("Server is ready to accept connections on %s", addr)
	// Serve using the created listener
	if err := s.server.Serve(listener); err != nil && !errors.Is(err, http.ErrServerClosed) {
		s.running = false
		return fmt.Errorf("failed to start server: %w", err)
	}

	return nil
}

func (s *Server) IsRunning() bool {
	return s.running
}

func (s *Server) Ready() chan struct{} {
	return s.ready
}

func (s *Server) Stop(ctx context.Context) error {
	if !s.running {
		return errors.New("server is not running")
	}

	if s.server == nil {
		return errors.New("server instance is nil")
	}

	log.Println("Shutting down HTTP server...")

	if err := s.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("server shutdown failed: %w", err)
	}

	s.running = false
	log.Println("HTTP server stopped")
	return nil
}

func (s *Server) Wait() {
	<-s.ready
}
