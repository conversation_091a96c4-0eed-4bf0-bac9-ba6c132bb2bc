FROM golang:1.24-alpine AS builder

WORKDIR /app

# Install build dependencies
RUN apk add --no-cache git

# Copy go mod and sum files
COPY go.mod ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application with debugging information
RUN CGO_ENABLED=0 GOOS=linux go build -gcflags="all=-N -l" -o /app/main ./main.go

# Install Delve debugger
RUN go install github.com/go-delve/delve/cmd/dlv@latest

# Use a small image for the final stage
FROM alpine:latest

WORKDIR /app

# Install runtime dependencies
RUN apk add --no-cache ca-certificates

# Copy the binary and debugger from the builder stage
COPY --from=builder /app/templates /app/templates
COPY --from=builder /app/main /app/main
COPY --from=builder /go/bin/dlv /app/dlv

# Expose Delve API port
EXPOSE 2345 8080

# Run Delve in headless mode for remote debugging
CMD ["/app/dlv", "--continue", "--listen=:2345", "--headless=true", "--api-version=2", "--accept-multiclient", "--log", "--log-output=debugger,debuglineerr,gdbwire,lldbout,rpc", "exec", "./main"]
# CMD [ "/app/main" ]
