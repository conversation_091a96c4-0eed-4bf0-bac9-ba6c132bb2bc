
# Define services.
services:
  postgres:
    image: postgres:16.3-alpine
    ports:
      - '5432:5432'
    environment:
      POSTGRES_USER: demo
      POSTGRES_PASSWORD: demo_password
      POSTGRES_DB: demo_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U demo -d demo_db"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    profiles:
      - dev
      - test
      - staging
      - prod
  
  app-replicas:
    build:
      context: .
      dockerfile: Dockerfile
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - DATABASE_URL=*******************************************/demo_db?sslmode=disable
    command: ["/app/main"]
    deploy:
      replicas: 3
    profiles:
      - dev
      - staging
  app:
    build:
      context: .
      dockerfile: Dockerfile
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - DATABASE_URL=*******************************************/demo_db?sslmode=disable
    ports:
      - '8080:8080'
      - '2345:2345'  # Expose Delve debugger port
    security_opt:
      - "seccomp:unconfined"  # Required for Delve to work properly
    cap_add:
      - SYS_PTRACE  # Required for Delve to work properly
    profiles:
      - dev
      - staging
  
  pgadmin:
    image: dpage/pgadmin4
    ports:
      - '5050:80'
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    profiles:
      - dev
      - test
      - staging
  
  # New service for database migrations
  # migrations:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   command: /app/main db migrate
  #   depends_on:
  #     postgres:
  #       condition: service_healthy
  #   environment:
  #     - DATABASE_URL=*******************************************/demo_db?sslmode=disable
  #   profiles:
  #     - dev
  #     - test
  #     - prod

volumes:
  postgres_data:
  pgadmin_data:
