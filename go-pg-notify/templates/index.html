<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Stream Monitor</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'pulse-green': 'pulseGreen 2s infinite',
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes pulseGreen {
            0%, 100% { background-color: rgb(34, 197, 94); }
            50% { background-color: rgb(22, 163, 74); }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Event Stream Monitor</h1>
            <p class="text-gray-600">Real-time PostgreSQL notification events</p>
            
            <!-- Connection Status -->
            <div class="mt-4 flex items-center">
                <div id="status-indicator" class="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                <span id="status-text" class="text-sm text-gray-600">Connecting...</span>
            </div>
            
            <!-- Stats -->
            <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600" id="total-messages">0</div>
                    <div class="text-sm text-blue-800">Total Messages</div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-green-600" id="messages-per-minute">0</div>
                    <div class="text-sm text-green-800">Messages/Min</div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600" id="uptime">00:00:00</div>
                    <div class="text-sm text-purple-800">Uptime</div>
                </div>
            </div>
        </div>

        <!-- Controls -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Test Publisher</h2>
            <div class="flex gap-4">
                <input 
                    type="text" 
                    id="message-input" 
                    placeholder="Enter message to publish..." 
                    class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                <button 
                    onclick="publishMessage()" 
                    class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                    Publish
                </button>
                <button 
                    onclick="clearMessages()" 
                    class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                    Clear
                </button>
            </div>
        </div>

        <!-- Messages Container -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Live Events</h2>
            <div id="messages-container" class="space-y-3 max-h-96 overflow-y-auto">
                <div class="text-gray-500 text-center py-8">
                    Waiting for events...
                </div>
            </div>
        </div>
    </div>

    <script>
        let eventSource;
        let messageCount = 0;
        let messagesInLastMinute = [];
        let startTime = Date.now();

        function initEventSource() {
            eventSource = new EventSource('/events/stream');
            
            eventSource.onopen = function(event) {
                updateConnectionStatus(true);
            };
            
            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    console.log('Received message:', data);
                    
                    if (data.type === 'connected') {
                        console.log('Connected to event stream');
                        return;
                    }
                    
                    if (data.type === 'shutdown') {
                        confirm('Server is shutting down. Do you want to continue?') && window.location.reload();
                        return;
                    }

                    if (data.type === 'heartbeat') {
                        // Just ignore heartbeat messages, they keep connection alive
                        return;
                    }
                    
                    addMessage(data);
                    updateStats();
                } catch (error) {
                    console.error('Error parsing message:', error);
                }
            };
            
            eventSource.onerror = function(event) {
                updateConnectionStatus(false);
                console.error('EventSource failed:', event);
            };
        }

        function updateConnectionStatus(connected) {
            const indicator = document.getElementById('status-indicator');
            const text = document.getElementById('status-text');
            
            if (connected) {
                indicator.className = 'w-3 h-3 rounded-full bg-green-500 mr-2 animate-pulse-green';
                text.textContent = 'Connected';
                text.className = 'text-sm text-green-600';
            } else {
                indicator.className = 'w-3 h-3 rounded-full bg-red-500 mr-2';
                text.textContent = 'Disconnected';
                text.className = 'text-sm text-red-600';
            }
        }

        function addMessage(data) {
            const container = document.getElementById('messages-container');
            
            // Remove "waiting" message if it exists
            if (container.children.length === 1 && container.children[0].textContent.includes('Waiting')) {
                container.innerHTML = '';
            }
            
            const messageDiv = document.createElement('div');
            messageDiv.className = 'bg-gray-50 border border-gray-200 rounded-lg p-4 animate-fade-in';
            
            const timestamp = new Date(data.timestamp).toLocaleTimeString();
            
            messageDiv.innerHTML = `
                <div class="flex justify-between items-start mb-2">
                    <div class="text-sm font-medium text-gray-800">Message ID: ${data.id}</div>
                    <div class="text-xs text-gray-500">${timestamp}</div>
                </div>
                <div class="text-lg text-gray-900 mb-2">${data.payload}</div>
                <div class="text-xs text-gray-600">
                    <span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2">
                        Source: ${data.metadata.source || 'unknown'}
                    </span>
                    <span class="inline-block bg-green-100 text-green-800 px-2 py-1 rounded">
                        Topic: {{.Topic}}
                    </span>
                </div>
            `;
            
            container.insertBefore(messageDiv, container.firstChild);
            
            // Keep only last 50 messages
            while (container.children.length > 50) {
                container.removeChild(container.lastChild);
            }
            
            messageCount++;
            messagesInLastMinute.push(Date.now());
        }

        function updateStats() {
            // Update total messages
            document.getElementById('total-messages').textContent = messageCount;
            
            // Update messages per minute
            const now = Date.now();
            messagesInLastMinute = messagesInLastMinute.filter(time => now - time < 60000);
            document.getElementById('messages-per-minute').textContent = messagesInLastMinute.length;
            
            // Update uptime
            const uptime = Math.floor((now - startTime) / 1000);
            const hours = Math.floor(uptime / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const seconds = uptime % 60;
            document.getElementById('uptime').textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        function publishMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (!message) {
                alert('Please enter a message');
                return;
            }
            
            fetch(`/publish/${encodeURIComponent(message)}`, {
                method: 'GET'
            })
            .then(response => response.text())
            .then(data => {
                console.log('Message published:', data);
                input.value = '';
            })
            .catch(error => {
                console.error('Error publishing message:', error);
                alert('Failed to publish message');
            });
        }

        function clearMessages() {
            const container = document.getElementById('messages-container');
            container.innerHTML = '<div class="text-gray-500 text-center py-8">Waiting for events...</div>';
            messageCount = 0;
            messagesInLastMinute = [];
            updateStats();
        }

        // Handle Enter key in input
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                publishMessage();
            }
        });

        // Update stats every second
        setInterval(updateStats, 1000);

        // Initialize
        initEventSource();
    </script>
</body>
</html>