


- [https://www.youtube.com/watch?v=DOaDpHh1FsQ](https://www.youtube.com/watch?v=DOaDpHh1FsQ)


## Bun - Changes

vendor/github.com/uptrace/bun/driver/pgdriver/listener.go

```go

// Added this type
type channelOverflowHandler func(n Notification)

// Added this function
func WithChannelOverflowHandler(handler channelOverflowHandler) ChannelOption {
	return func(c *channel) {
		c.overflowHandler = handler
	}
}

type channel struct {
	ctx context.Context
	ln  *Listener

	size        int
	pingTimeout time.Duration

	ch              chan Notification
	pingCh          chan struct{}
    // Added this field
	overflowHandler channelOverflowHandler
}


func (c *channel) startReceive() {
	var errCount int
	for {
		channel, payload, err := c.ln.Receive(c.ctx)
		if err != nil {
			if err == errListenerClosed {
				close(c.ch)
				return
			}

			if errCount > 0 {
				time.Sleep(500 * time.Millisecond)
			}
			errCount++

			continue
		}

		errCount = 0

		// Any notification is as good as a ping.
		select {
		case c.pingCh <- struct{}{}:
		default:
		}

		switch channel {
		case pingChannel:
			// ignore
		default:
			select {
			case c.ch <- Notification{channel, payload}:
			default:
				Logger.Printf(c.ctx, "pgdriver: Listener buffer is full (message is dropped)")
                // Added this lines
				if c.overflowHandler != nil {
					c.overflowHandler(Notification{channel, payload})
				}
			}
		}
	}
}


```

**Usage**

```go
    channelSize := 1000
    notifCh := s.ln.Channel(
		pgdriver.WithChannelSize(channelSize),
		pgdriver.WithChannelOverflowHandler(func(n pgdriver.Notification) {
			log.Printf("Channel overflow: %v", n)
            // do something with the notification, 
            // save it somewhere or drop it if you want to.
		}),
	)
```