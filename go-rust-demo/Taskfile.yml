# https://taskfile.dev
# - export LD_LIBRARY_PATH="$LD_LIBRARY_PATH:$(pwd)/rust_lib/target/release"

version: '3'

tasks:
  go-run:
    cmds:
      - go run main.go
    silent: true
  go-build:
    cmds:
      - go build -o tmp/main main.go 
    silent: true
  cargo-build:
    cmds:
      - cargo build --profile=release
      - uniffi-bindgen-go --library target/release/libgo_rust.so --out-dir out
    silent: true
    dir: rust_lib