
use calamine::{open_workbook, Reader, Xlsx};
use polars::prelude::*;
use std::fs::File;
use rayon::prelude::*;

uniffi::setup_scaffolding!();

#[derive(uniffi::Error, Debug)]
pub enum MyError {
    Generic { message: String },
}

impl std::error::Error for MyError {}



impl std::fmt::Display for MyError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MyError::Generic { message } => write!(f, "{}", message),
        }
    }
}

#[uniffi::export]
pub fn convert_excel_to_csv(excel_file: String, sheet_name: String, csv_file: String) -> Result<String, MyError> {
    // Explicitly specify that we are working with an Xlsx workbook
    match open_workbook::<Xlsx<_>, _>(&excel_file) {
        Ok(mut workbook) => {
            if let Ok(range) = workbook.worksheet_range(&sheet_name) {
                let data: Vec<Vec<String>> = range.rows()
                    .map(|row| row.iter().map(ToString::to_string).collect())
                    .collect();

                let columns: Vec<String> = (0..data[0].len())
                    .map(|i| format!("column_{}", i))
                    .collect();

                let series_vec: Vec<Series> = (0..columns.len())
                    .into_par_iter()
                    .map(|i| {
                        let column: Vec<String> = data.iter().map(|row| row[i].clone()).collect();
                        Series::new((&columns[i]).into(), column)
                    })
                    .collect();

                match DataFrame::new(series_vec) {
                    Ok(mut df) => {
                        if let Ok(mut file) = File::create(&csv_file) {
                            if CsvWriter::new(&mut file).finish(&mut df).is_ok() {
                                Ok("Successfully converted the Excel file to CSV.".to_string())
                            } else {
                                Err(MyError::Generic { message: format!("Failed to write CSV file: {}", csv_file) })
                            }
                        } else {
                            Err(MyError::Generic { message: format!("Failed to create CSV file: {}", csv_file) })
                        }
                    }
                    Err(e) => Err(MyError::Generic { message: format!("Failed to create DataFrame: {}", e) }),
                }
            } else {
                Err(MyError::Generic { message: format!("Failed to find sheet '{}' in the Excel file.", sheet_name) })
            }
        }
        Err(e) => Err(MyError::Generic { message: format!("Failed to open Excel file '{}': {}", excel_file, e) }),
    }
}



