[package]
name = "go_rust"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
polars = { version = "0.43.1" }
calamine = "0.25"
rayon = "1.5"
uniffi = { version = "0.25.3", features = ["cli"] }
anyhow = "1.0"
thiserror = "1.0"
encoding_rs = "0.8.32"

[lib]
crate_type = ["cdylib"]
name = "go_rust"

[[bin]]
# This can be whatever name makes sense for your project, but the rest of this tutorial assumes uniffi-bindgen.
name = "uniffi-bindgen"
path = "uniffi-bindgen.rs"


# Release profile configuration for optimizing the binary
[profile.release]
opt-level = 3     # Use the highest optimization level (default for release)
lto = "thin"      # Enable link-time optimization (LTO) for smaller binaries
codegen-units = 1 # Reduces the number of codegen units to increase optimization
debug = false     # Strips debug symbols for smaller binaries
panic = 'abort'   # Optimize panic handling by using abort instead of unwind
