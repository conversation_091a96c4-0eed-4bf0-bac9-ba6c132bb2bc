

## Download a big csv file and convert it to excel file

You can download a big csv file from here: 

- https://www.kaggle.com/datasets/ksabishek/massive-bank-dataset-1-million-rows
- https://www.kaggle.com/datasets/polartech/flight-data-with-1-million-or-more-records
- https://www.kaggle.com/datasets/ahmadrafiee/911-calls-for-service-metadata-1-million-record
- https://www.kaggle.com/datasets/uciml/pima-indians-diabetes-database
- https://markbdsouza.github.io/csv-data-generator/


## How to run the project

```bash
task cargo-build

task go-build

time tmp/main

```