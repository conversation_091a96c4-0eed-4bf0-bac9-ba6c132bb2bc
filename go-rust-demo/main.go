package main

/*
#cgo LDFLAGS: -L./rust_lib/target/release -lgo_rust -Wl,-rpath=./rust_lib/target/release
#cgo CFLAGS: -I./rust_lib/out/go_rust
#include "./rust_lib/out/go_rust/go_rust.h"
*/
import "C"

import (
	"demo/rust_lib/out/go_rust" // Import the generated go_rust package
	"fmt"
	"os"
)

func main() {
	// Default path for the Excel file
	excelFilePath := "data/input.xlsx"
	sheetName := "data"
	outputFilePath := "output.csv"

	// Check if an argument is passed and use it as the Excel file path
	if len(os.Args) > 1 {
		excelFilePath = os.Args[1]
	}

	if len(os.Args) > 2 {
		sheetName = os.Args[2]
	}

	// Call the ConvertExcelToCsv function from the generated Go bindings
	result, err := go_rust.ConvertExcelToCsv(excelFilePath, sheetName, outputFilePath)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}
	fmt.Println(result)
}
