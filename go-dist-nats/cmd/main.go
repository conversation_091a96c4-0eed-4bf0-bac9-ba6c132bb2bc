package main

import (
	"context"
	"fmt"
	"log"
	"sync"

	"github.com/emilioforrer/go-playground/go-dist-nats/server"
)

type Env struct{}

func main() {
	ctx := context.Background()
	err := run(ctx)
	if err != nil {
		log.Fatal(err)
	}
}

func loadEnv() *Env {
	return &Env{}
}

func run(ctx context.Context) error {
	var wg sync.WaitGroup
	env := loadEnv()

	// Start the server in a goroutine
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := startServer(ctx, env); err != nil {
			// Handle the error appropriately
			log.Printf("startServer failed: %v", err)
		}
	}()

	// Wait for the goroutine to finish
	wg.Wait()

	log.Println("App exited")

	return nil
}

func startServer(ctx context.Context, env *Env) error {
	s := server.New()
	err := s.Run()
	if err != nil {
		return fmt.Errorf("error in start server %w: ", err)
	}
	return nil
}
