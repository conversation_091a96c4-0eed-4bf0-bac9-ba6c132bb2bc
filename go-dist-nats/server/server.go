package server

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"
)

type Server interface {
	Run() error
}

// server is a wrapper around http.Server
type server struct {
	http.Server
}

// Ensure server implements the Server interface
// at compile time
var _ Server = (*server)(nil)

type option func(*server)

func New(opts ...option) Server {
	s := &server{
		Server: http.Server{
			Addr:              fmt.Sprintf("0.0.0.0:%d", 8080),
			ReadTimeout:       10 * time.Second,
			WriteTimeout:      10 * time.Second,
			MaxHeaderBytes:    1 << 20, // 1 MB
			ReadHeaderTimeout: 10 * time.Second,
			IdleTimeout:       10 * time.Second,
		},
	}
	for _, opt := range opts {
		opt(s)
	}
	return s
}

func (s *server) Run() error {
	// Channel to listen for termination signals
	termSignal := make(chan os.Signal, 1)
	signal.Notify(termSignal, os.Interrupt, syscall.SIGTERM)

	// Start the HTTP server in a goroutine
	go func() {
		if err := s.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Printf("Error: %s\n", err)
		}
	}()

	// Wait for termination signal
	<-termSignal

	// Graceful shutdown with a 5-second timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := s.Shutdown(ctx); err != nil {
		return fmt.Errorf("server shutdown failed: %v", err)
	}

	return nil
}
