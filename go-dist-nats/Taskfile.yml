# https://taskfile.dev

version: "3"

tasks:
  install:
    cmds:
      - go install github.com/air-verse/air@latest
      - go install golang.org/x/tools/gopls@latest
      - go install github.com/fatih/gomodifytags@latest
      - go install honnef.co/go/tools/cmd/staticcheck@latest
      - go install honnef.co/go/tools/cmd/structlayout-optimize@latest
      - go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
      - go get github.com/go-delve/delve/cmd/dlv
  test:
    cmds:
      - go test -timeout 30s -v ./...
