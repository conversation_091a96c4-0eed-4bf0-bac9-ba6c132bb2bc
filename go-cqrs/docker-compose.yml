
# Define services.
services:
  postgres:
    image: postgres:16.3-alpine
    ports:
      - '5432:5432'
    environment:
      POSTGRES_USER: demo
      POSTGRES_PASSWORD: demo_password
      POSTGRES_DB: demo_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U demo -d demo_db"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
  pgadmin:
    image: dpage/pgadmin4
    ports:
      - '5050:80'
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres

volumes:
  postgres_data:
  pgadmin_data: