package migrations

import (
	"context"
	"demo/model"
	"fmt"

	"github.com/uptrace/bun"
)

func init() {
	Migrations.MustRegister(func(ctx context.Context, db *bun.DB) error {
		_, err := db.NewCreateTable().
			Model((*model.Message)(nil)).
			IfNotExists().
			Exec(ctx)
		if err != nil {
			return fmt.Errorf("failed to create messages table: %w", err)
		}

		// Create indexes for common queries
		_, err = db.ExecContext(ctx, `
            CREATE INDEX IF NOT EXISTS idx_messages_status ON messages (status);
            CREATE INDEX IF NOT EXISTS idx_messages_topic ON messages (topic);
            CREATE INDEX IF NOT EXISTS idx_messages_scheduled_at ON messages (scheduled_at);
            CREATE INDEX IF NOT EXISTS idx_messages_locked_until ON messages (locked_until);
            CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages (created_at);
        `)
		if err != nil {
			return fmt.Errorf("failed to create indexes: %w", err)
		}

		return nil
	}, func(ctx context.Context, db *bun.DB) error {
		_, err := db.NewDropTable().
			Model((*model.Message)(nil)).
			IfExists().
			Cascade().
			Exec(ctx)
		return err
	})
}
