package server

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net"
	"net/http"
	"time"
)

type Options struct {
	Host            string
	Port            int
	ReadTimeout     time.Duration
	WriteTimeout    time.Duration
	ShutdownTimeout time.Duration
}

func DefaultOptions() *Options {
	return &Options{
		Host:            "0.0.0.0",
		Port:            8080,
		ReadTimeout:     15 * time.Second,
		WriteTimeout:    15 * time.Second,
		ShutdownTimeout: 30 * time.Second,
	}
}

type OptionsFunc func(*Options)

// WithHost sets the host address
func WithHost(host string) OptionsFunc {
	return func(o *Options) {
		o.Host = host
	}
}

// WithPort sets the port number
func WithPort(port int) OptionsFunc {
	return func(o *Options) {
		o.Port = port
	}
}

// WithTimeouts sets all timeout values
func WithTimeouts(read, write, shutdown time.Duration) OptionsFunc {
	return func(o *Options) {
		o.ReadTimeout = read
		o.WriteTimeout = write
		o.ShutdownTimeout = shutdown
	}
}

type Server struct {
	options *Options
	mux     *http.ServeMux
	server  *http.Server
	running bool
	ready   chan struct{}
}

func New(mux *http.ServeMux, opts ...OptionsFunc) *Server {
	options := DefaultOptions()

	for _, opt := range opts {
		opt(options)
	}

	return &Server{
		mux:     mux,
		options: options,
		ready:   make(chan struct{}, 1),
	}
}

func (s *Server) Start() error {
	if s.running {
		return errors.New("server is already running")
	}

	addr := fmt.Sprintf("%s:%d", s.options.Host, s.options.Port)

	s.server = &http.Server{
		Addr:         addr,
		Handler:      s.mux,
		ReadTimeout:  s.options.ReadTimeout,
		WriteTimeout: s.options.WriteTimeout,
	}

	// Create listener before starting server
	listener, err := net.Listen("tcp", addr)
	if err != nil {
		return fmt.Errorf("failed to create listener: %w", err)
	}

	s.running = true
	log.Printf("Starting HTTP server on %s", addr)

	// Signal that the server is ready to accept connections
	close(s.ready)

	// Serve using the created listener
	if err := s.server.Serve(listener); err != nil && !errors.Is(err, http.ErrServerClosed) {
		s.running = false
		return fmt.Errorf("failed to start server: %w", err)
	}

	return nil
}

func (s *Server) IsRunning() bool {
	return s.running
}

func (s *Server) Ready() chan struct{} {
	return s.ready
}

func (s *Server) Stop() error {
	if !s.running {
		return errors.New("server is not running")
	}

	if s.server == nil {
		return errors.New("server instance is nil")
	}

	log.Println("Shutting down HTTP server...")

	// Create a context with timeout for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), s.options.ShutdownTimeout)
	defer cancel()

	if err := s.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("server shutdown failed: %w", err)
	}

	s.running = false
	log.Println("HTTP server stopped")
	return nil
}

func (s *Server) Wait() {
	<-s.ready
}
