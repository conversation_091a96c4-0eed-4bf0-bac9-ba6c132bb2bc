package handlers

import (
	"demo/app"
	"demo/cqrs"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
)

type PublisherController struct {
	Requester *cqrs.PubSubBoostrapper[cqrs.Request, cqrs.Response]
	Topic     string
}

func (pc *PublisherController) Publish() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		txtMsg := r.PathValue("message")
		if txtMsg == "" {
			log.Println("Empty message received")
			http.Error(w, "Message cannot be empty", http.StatusBadRequest)
			return
		}

		log.Printf("Publishing message to topic: %s\n", pc.Topic)

		// Create a proper JSON object for the command
		commandObj := map[string]interface{}{"message": txtMsg}
		jsonMsg, err := json.Marshal(commandObj)
		if err != nil {
			log.Printf("Failed to marshal message: %v\n", err)
			http.Error(w, "Failed to process message", http.StatusInternalServerError)
			return
		}

		// Send the request with proper JSON
		req := cqrs.Request{
			Command: jsonMsg,
		}

		log.Printf("Sending request with command: %s\n", string(jsonMsg))

		resp, err := pc.Requester.Request(req)
		if err != nil {
			log.Printf("Failed to publish message: %v\n", err)
			http.Error(w, "Failed to publish message", http.StatusInternalServerError)
			return
		}

		log.Printf("Published message, response: %+v\n", resp)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(fmt.Sprintf(`{"status":"success","message":"Message published successfully","response":"%+v"}`, resp)))
	}
}

func RegisterHandlers(app app.App) {
	pc := &PublisherController{
		Requester: app.Requester,
		Topic:     "default_topic", // Set the default topic
	}
	app.Router.HandleFunc("/publish/{message}", pc.Publish())
}
