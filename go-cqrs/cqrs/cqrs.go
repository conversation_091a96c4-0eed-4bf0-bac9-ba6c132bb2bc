package cqrs

import (
	"context"
	"encoding/json"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill/components/cqrs"
	"github.com/ThreeDotsLabs/watermill/components/requestreply"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/ThreeDotsLabs/watermill/pubsub/gochannel"
)

type Request struct {
	Command json.RawMessage `json:"command"`
}

type Response struct {
	Result json.RawMessage `json:"result"`
}
type ResponseMarshaler struct {
}

func (r ResponseMarshaler) MarshalReply(params requestreply.BackendOnCommandProcessedParams[Response]) (*message.Message, error) {
	payload, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}
	return message.NewMessage(watermill.NewUUID(), payload), nil
}

func (r ResponseMarshaler) UnmarshalReply(msg *message.Message) (reply requestreply.Reply[Response], err error) {
	reply.HandlerResult = Response{
		Result: json.RawMessage(msg.Payload),
	}
	return reply, nil
}

type PubSubBoostrapper[Req, Resp any] struct {
	publisher    message.Publisher
	subscriber   message.Subscriber
	logger       watermill.LoggerAdapter
	requester    cqrs.CommandHandler
	backend      requestreply.Backend[Resp]
	cmdBus       *cqrs.CommandBus
	cmdProcessor *cqrs.CommandProcessor
	router       *message.Router
}

func NewPubSubBoostrapper[Req, Resp any](topic string, marshaller requestreply.BackendPubsubMarshaler[Resp], handleFunc func(ctx context.Context, cmd *Req) (Resp, error)) *PubSubBoostrapper[Req, Resp] {
	logger := watermill.NewStdLogger(false, false)
	pubSub := gochannel.NewGoChannel(gochannel.Config{}, logger)

	cmdBus, err := cqrs.NewCommandBusWithConfig(
		pubSub,
		cqrs.CommandBusConfig{
			Marshaler: cqrs.JSONMarshaler{},
			GeneratePublishTopic: func(params cqrs.CommandBusGeneratePublishTopicParams) (string, error) {
				return topic, nil
			},
			Logger: logger,
		},
	)
	if err != nil {
		panic(err)
	}

	router, err := message.NewRouter(message.RouterConfig{}, logger)
	if err != nil {
		panic(err)
	}

	cmdProcessor, err := cqrs.NewCommandProcessorWithConfig(
		router,
		cqrs.CommandProcessorConfig{
			Logger:    logger,
			Marshaler: cqrs.JSONMarshaler{},
			GenerateSubscribeTopic: func(params cqrs.CommandProcessorGenerateSubscribeTopicParams) (string, error) {
				return topic, nil
			},
			SubscriberConstructor: func(params cqrs.CommandProcessorSubscriberConstructorParams) (message.Subscriber, error) {
				return pubSub, nil
			},
		},
	)
	if err != nil {
		panic(err)
	}

	backend, err := requestreply.NewPubSubBackend(requestreply.PubSubBackendConfig{
		Publisher: pubSub,
		SubscriberConstructor: func(requestreply.PubSubBackendSubscribeParams) (message.Subscriber, error) {
			return pubSub, nil
		},
		GenerateSubscribeTopic: func(requestreply.PubSubBackendSubscribeParams) (string, error) {
			return topic, nil
		},
		GeneratePublishTopic: func(requestreply.PubSubBackendPublishParams) (string, error) {
			return topic, nil
		},
	}, marshaller)
	if err != nil {
		panic(err)
	}
	requester := requestreply.NewCommandHandlerWithResult(topic, backend, handleFunc)
	err = cmdProcessor.AddHandlers(requester)
	if err != nil {
		panic(err)
	}

	return &PubSubBoostrapper[Req, Resp]{
		publisher:    pubSub,
		subscriber:   pubSub,
		logger:       logger,
		requester:    requester,
		backend:      backend,
		cmdBus:       cmdBus,
		cmdProcessor: cmdProcessor,
		router:       router,
	}
}

func (p *PubSubBoostrapper[Req, Resp]) Start() {
	go func() {
		err := p.router.Run(context.Background())
		if err != nil {
			panic(err)
		}
	}()
	<-p.router.Running()

}

func (p *PubSubBoostrapper[Req, Resp]) Running() <-chan struct{} {
	return p.router.Running()
}

func (p *PubSubBoostrapper[Req, Resp]) Stop() {
	p.router.Close()
	p.subscriber.Close()
	p.publisher.Close()
}

func (p *PubSubBoostrapper[Req, Resp]) Request(req Req) (requestreply.Reply[Resp], error) {
	reply, err := requestreply.SendWithReply(context.Background(), p.cmdBus, p.backend, req)
	if err != nil {
		var zero requestreply.Reply[Resp]
		return zero, err
	}

	return reply, nil
}
func (p *PubSubBoostrapper[Req, Resp]) Subscribe(replyTopic string) {
	subscriber := p.subscriber

	// Subscribe to the "requests" topic
	messages, err := subscriber.Subscribe(context.Background(), replyTopic)
	if err != nil {
		p.logger.Error("subscribe error", err, nil)
		return
	}

	for msg := range messages {
		var req Req
		if err := json.Unmarshal(msg.Payload, &req); err != nil {
			p.logger.Error("unmarshal request", err, nil)
			msg.Nack()
			continue
		}

		msg.Ack()
	}
}
