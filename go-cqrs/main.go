package main

import (
	"context"
	"demo/app"
	"demo/config"
	"demo/cqrs"
	"demo/database"
	"demo/http/handlers"
	"demo/http/server"
	"encoding/json"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/uptrace/bun"
)

// ---- Main ----
func main() {
	conf := config.New()
	mux := http.NewServeMux()
	srv := server.New(mux)
	db := database.New()
	requester := cqrs.NewPubSubBoostrapper(
		"demo_topic",
		cqrs.ResponseMarshaler{},
		func(ctx context.Context, cmd *cqrs.Request) (cqrs.Response, error) {
			// Properly handle the command
			log.Printf("Received command: %s\n", string(cmd.Command))

			// Create a proper JSON response
			resultData := map[string]interface{}{
				"status": "success",
				"data":   nil,
			}
			jsonResult, err := json.Marshal(resultData)
			if err != nil {
				log.Printf("Failed to marshal result: %v\n", err)
				return cqrs.Response{}, err
			}

			resp := cqrs.Response{
				Result: jsonResult,
			}
			log.Printf("Sending response: %s\n", string(resp.Result))

			return resp, nil
		},
	)

	// Start the requester before setting up the subscription
	go requester.Start()
	<-requester.Running()

	// Set up the subscription after the requester is running
	go requester.Subscribe("demo_topic")

	// Set up context with cancellation for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Set connection pool parameters
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	waitForDB(ctx, db)

	app := app.App{
		Server:    srv,
		Router:    mux,
		Config:    &conf,
		DB:        db,
		Requester: requester,
	}

	// Create Bun DB instance

	defer db.Close()

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	handlers.RegisterHandlers(app)
	go func() {
		if err := srv.Start(); err != nil {
			log.Printf("Server error: %v", err)
		}
	}()

	defer func() {
		if err := srv.Stop(); err != nil {
			log.Printf("Error closing server: %v", err)
		}
		requester.Stop()
	}()

	// Wait for the server to be ready
	<-srv.Ready()
	log.Println("Server is ready to accept connections")

	// Wait for termination signal
	sig := <-sigChan
	log.Printf("Received signal: %v, shutting down\n", sig)
	cancel() // Cancel the context to initiate graceful shutdown

	// Allow time for cleanup
	log.Println("Shutting down...")
	log.Println("Shutdown complete")
}

func waitForDB(ctx context.Context, db *bun.DB) {
	// Verify database connection with retry
	maxRetries := 3
	var err error
	for i := 0; i < maxRetries; i++ {
		ctxTimeout, cancelTimeout := context.WithTimeout(ctx, 5*time.Second)
		err = db.PingContext(ctxTimeout)
		cancelTimeout()

		if err == nil {
			log.Printf("Successfully connected to the database on attempt %d", i+1)
			break
		}

		if i < maxRetries-1 {
			log.Printf("Database connection attempt %d failed: %v. Retrying...", i+1, err)
			time.Sleep(2 * time.Second)
		}
	}

	if err != nil {
		log.Fatalf("Failed to connect to database after %d attempts: %v", maxRetries, err)
	}
}
