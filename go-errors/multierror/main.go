package main

import (
	"errors"
	"fmt"
	"strings"
)

type ErrorDetail struct {
	Message string
}

type MultiError struct {
	errorDetails []ErrorDetail
	wrapped      error
}

// Errors returns all error details, including the wrapped error if present
// Errors returns all error details, with the wrapped error at the beginning if present
func (m *MultiError) Errors() []ErrorDetail {
	// If there's no wrapped error, just return a copy of the existing details
	if m.wrapped == nil {
		// If there are no errors at all, return an empty slice without allocation
		if len(m.errorDetails) == 0 {
			return nil
		}

		// Pre-allocate the exact size needed
		result := make([]ErrorDetail, len(m.errorDetails))
		copy(result, m.errorDetails)
		return result
	}

	// If we have a wrapped error, pre-allocate with the exact size needed
	result := make([]ErrorDetail, 1+len(m.errorDetails))

	// Add wrapped error at the beginning
	result[0] = ErrorDetail{Message: m.wrapped.Error()}

	// Copy the rest of the errors in one operation
	if len(m.errorDetails) > 0 {
		copy(result[1:], m.errorDetails)
	}

	return result
}

func (m *MultiError) Add(msg string) {
	m.errorDetails = append(m.errorDetails, ErrorDetail{Message: msg})
}

func (m *MultiError) Error() string {
	var messages []string
	for _, err := range m.errorDetails {
		messages = append(messages, err.Message)
	}
	result := strings.Join(messages, ", ")
	if m.wrapped != nil {
		result += ": " + m.wrapped.Error()
	}
	return result
}

func (m *MultiError) HasErrors() bool {
	return len(m.errorDetails) > 0 || m.wrapped != nil
}

func (m *MultiError) Unwrap() error {
	return m.wrapped
}

func (m *MultiError) Wrap(err error) {
	m.wrapped = fmt.Errorf("%w", err)
}

type User struct {
	Name  string `json:"name"`
	Email string `json:"email"`
	Age   int    `json:"age"`
}

func ValidateUser(user User) *MultiError {
	me := &MultiError{}

	if user.Name == "" {
		me.Add("Name is required")
	}
	if user.Email == "" || !strings.Contains(user.Email, "@") {
		me.Add("Invalid email address")
	}
	if user.Age < 18 {
		me.Add("Age must be 18 or older")
	}

	if me.HasErrors() {
		me.Wrap(errors.New("validation failed"))
		return me
	}
	return nil
}

func main() {
	var user User

	err := ValidateUser(user)
	if err != nil {
		if errors.Is(err, errors.New("validation failed")) {
			fmt.Println("Detected wrapped validation error using errors.Is")
		}

		var multiErr *MultiError
		if errors.As(err, &multiErr) {
			fmt.Println("Extracted MultiError using errors.As:", multiErr.Errors()) // Updated to use Errors() method
		}

		// Unwrap the error and log it if present
		if unwrappedErr := errors.Unwrap(err); unwrappedErr != nil {
			fmt.Println("Unwrapped error:", unwrappedErr)
		}
	}
}
