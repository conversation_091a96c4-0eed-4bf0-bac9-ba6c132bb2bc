package subscriber

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill-nats/v2/pkg/nats"
	"github.com/failsafe-go/failsafe-go"
	"github.com/failsafe-go/failsafe-go/retrypolicy"
	natssdk "github.com/nats-io/nats.go"
)

func Subscribe(name string, clientURL string) error {
	logger := watermill.NewStdLogger(false, false)

	// Connect to NATS
	nc, err := natssdk.Connect(clientURL)
	if err != nil {
		return fmt.Errorf("failed to connect to NATS: %v", err)
	}
	defer nc.Close()

	// Get JetStream context
	js, err := nc.JetStream()
	if err != nil {
		return fmt.Errorf("failed to get JetStream context: %v", err)
	}

	// Create a stream that captures the "demo.>" subject
	_, err = js.AddStream(&natssdk.StreamConfig{
		Name:     "DEMO",
		Subjects: []string{"demo.>"},
	})
	if err != nil {
		logger.Info("Stream creation error (might already exist)", watermill.LogFields{"error": err})
	}

	// Create a consumer group configuration
	consumerGroup := "my-consumer-group"

	// Create a Watermill NATS subscriber with consumer group configuration
	subscriber, err := nats.NewSubscriber(
		nats.SubscriberConfig{
			URL:              clientURL,
			QueueGroupPrefix: consumerGroup,
			SubscribersCount: 3,
		},
		logger,
	)
	if err != nil {
		return fmt.Errorf("failed to create NATS subscriber: %v", err)
	}

	// Subscribe to messages
	messages, err := subscriber.Subscribe(context.Background(), "demo.>")
	if err != nil {
		return fmt.Errorf("failed to subscribe: %v", err)
	}

	for msg := range messages {
		logger.Info(fmt.Sprintf("Subscriber %s has received message: %s", name, string(msg.Payload)), nil)
		msg.Ack() // Important: acknowledge the message
	}

	return nil
}

func SafeSubscribe(name string, clientURL string) error {
	retryPolicy := retrypolicy.Builder[bool]().
		WithMaxRetries(3).
		WithBackoff(100*time.Millisecond, 30*time.Second).
		OnFailure(func(f failsafe.ExecutionEvent[bool]) {
			log.Printf("Failed to subscribe to 'Internal' topic: %v", f.LastError())
			log.Printf("Retrying to subscribe to 'Internal' topic...")
		}).
		OnSuccess(func(f failsafe.ExecutionEvent[bool]) {
			log.Printf("Successfully subscribed to 'Internal' topic")
		}).
		Build()

	// Get with retries
	_, err := failsafe.Get(func() (bool, error) {
		subscriberErr := Subscribe("Internal", clientURL)
		log.Printf("Subscribed to 'Internal' topic done: %v", subscriberErr)
		if subscriberErr != nil {
			return false, subscriberErr
		}
		return true, nil
	}, retryPolicy)
	if err != nil {
		log.Printf("Failed to subscribe to 'Internal' topic: %v", err)
		return fmt.Errorf("failed to subscribe to 'Internal' topic: %v", err)
	}
	return nil
}
