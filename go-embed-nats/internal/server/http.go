package server

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill-nats/v2/pkg/nats"
	"github.com/ThreeDotsLabs/watermill/message"
	natssdk "github.com/nats-io/nats.go"
)

// HTTPServer represents our HTTP server
type HTTPServer struct {
	port       int
	publisher  message.Publisher
	instanceID string
	server     *http.Server
	running    bool
}

// Message represents the message structure
type Message struct {
	Topic   string `json:"topic"`
	Content string `json:"content"`
}

// ResponseMessage includes the instance ID that processed the request
type ResponseMessage struct {
	Status     string `json:"status"`
	Message    string `json:"message"`
	InstanceID string `json:"instance_id"`
}

// NewHTTPServer creates a new HTTP server
func NewHTTPServer(port int, natsConn *natssdk.Conn) (*HTTPServer, error) {
	// Create a watermill logger
	logger := watermill.NewStdLogger(false, false)

	// Create a NATS publisher using the existing connection
	publisher, err := nats.NewPublisher(
		nats.PublisherConfig{
			NatsOptions: []natssdk.Option{
				natssdk.Timeout(30 * time.Second),                      // Increase timeout significantly
				natssdk.InProcessServer(natsConn.Opts.InProcessServer), // Use the in-process server
				natssdk.RetryOnFailedConnect(true),                     // Retry on failed connection
			},
			JetStream: nats.JetStreamConfig{
				Disabled:         false, // Enable JetStream
				SubscribeOptions: []natssdk.SubOpt{},
			},
		},
		logger,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create NATS publisher: %w", err)
	}

	// Get hostname or use environment variable for instance identification
	instanceID := os.Getenv("INSTANCE_ID")
	if instanceID == "" {
		var err error
		instanceID, err = os.Hostname()
		if err != nil {
			instanceID = fmt.Sprintf("unknown-%d", port)
		}
	}

	return &HTTPServer{
		port:       port,
		publisher:  publisher,
		instanceID: instanceID,
	}, nil
}

// Start starts the HTTP server
func (h *HTTPServer) Start() error {
	// Define the message handler
	mux := http.NewServeMux()
	mux.HandleFunc("/publish", h.handlePublish)

	// Define a health check endpoint
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		resp := ResponseMessage{
			Status:     "OK",
			Message:    "Service is healthy",
			InstanceID: h.instanceID,
		}
		json.NewEncoder(w).Encode(resp)
	})

	// Define an endpoint to get instance ID
	mux.HandleFunc("/instance", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		resp := map[string]string{
			"instance_id": h.instanceID,
		}
		json.NewEncoder(w).Encode(resp)
	})

	// Create the server
	addr := fmt.Sprintf(":%d", h.port)
	h.server = &http.Server{
		Addr:    addr,
		Handler: mux,
	}

	// Start the server
	log.Printf("HTTP server listening on %s (Instance ID: %s)", addr, h.instanceID)
	h.running = true
	return h.server.ListenAndServe()
}

// handlePublish handles the publish endpoint
func (h *HTTPServer) handlePublish(w http.ResponseWriter, r *http.Request) {
	log.Printf("[Instance: %s] Attempting to publish to topic %s", h.instanceID, "")

	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var msg Message
	if err := json.NewDecoder(r.Body).Decode(&msg); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if msg.Topic == "" {
		http.Error(w, "Topic is required", http.StatusBadRequest)
		return
	}

	if msg.Content == "" {
		http.Error(w, "Content is required", http.StatusBadRequest)
		return
	}

	// Add instance ID to the message content
	enhancedContent := fmt.Sprintf("[From Instance: %s at %s] %s", h.instanceID, time.Now().Format(time.RFC3339), msg.Content)

	// Create a watermill message
	wmMsg := message.NewMessage(watermill.NewUUID(), []byte(enhancedContent))

	// Publish the message
	if err := h.publisher.Publish(msg.Topic, wmMsg); err != nil {
		log.Printf("Failed to publish message: %v", err)
		http.Error(w, "Failed to publish message", http.StatusInternalServerError)
		return
	}

	log.Printf("[Instance: %s] Published message to topic %s: %s", h.instanceID, msg.Topic, msg.Content)

	// Return success response with instance ID
	w.WriteHeader(http.StatusOK)
	resp := ResponseMessage{
		Status:     "success",
		Message:    "Message published successfully",
		InstanceID: h.instanceID,
	}
	json.NewEncoder(w).Encode(resp)
}

// Shutdown gracefully shuts down the HTTP server with a timeout context
func (h *HTTPServer) Shutdown(ctx context.Context) error {
	// Close the publisher if it exists
	if h.publisher != nil {
		if err := h.publisher.Close(); err != nil {
			log.Printf("Error closing publisher: %v", err)
			// Continue with shutdown even if publisher close fails
		}
		log.Printf("Publisher closed successfully")
	}

	// Shutdown the server if it exists
	if h.server != nil {
		if err := h.server.Shutdown(ctx); err != nil {
			return fmt.Errorf("HTTP server shutdown error: %w", err)
		}
		log.Printf("HTTP server (Instance ID: %s) shutdown gracefully", h.instanceID)
	}

	h.running = false
	return nil
}

// Stop immediately stops the server (may interrupt connections)
func (h *HTTPServer) Stop() {
	if h.publisher != nil {
		h.publisher.Close()
	}

	if h.server != nil {
		h.server.Close()
		log.Printf("HTTP server (Instance ID: %s) stopped", h.instanceID)
	}

	h.running = false
}

// Ready returns a channel that signals when the server is ready
// This is a simple implementation - in a real-world scenario, you might want to
// implement a more sophisticated readiness check
func (h *HTTPServer) Ready() <-chan bool {
	ready := make(chan bool, 1)
	go func() {
		// Simple implementation: just wait a short time for server to start
		time.Sleep(100 * time.Millisecond)
		ready <- true
		close(ready)
	}()
	return ready
}

// IsRunning returns whether the server is currently running
func (h *HTTPServer) IsRunning() bool {
	return h.running
}

// Address returns the server's listening address
func (h *HTTPServer) Address() string {
	if h.server != nil {
		return h.server.Addr
	}
	return fmt.Sprintf(":%d", h.port)
}

// GetHTTPPort returns the HTTP port from environment or default
func GetHTTPPort() int {
	portStr := os.Getenv("HTTP_PORT")
	if portStr == "" {
		return 8080 // Default HTTP port
	}
	port, err := strconv.Atoi(portStr)
	if err != nil {
		return 8080
	}
	return port
}
