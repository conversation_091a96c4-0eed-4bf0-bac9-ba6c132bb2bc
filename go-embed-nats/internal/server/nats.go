package server

import (
	"context"
	"fmt"
	"log"
	"net"
	"os"
	"strconv"
	"time"

	"github.com/nats-io/nats-server/v2/server"
	"github.com/nats-io/nats.go"
)

// NATSServer represents our embedded NATS server
type NATSServer struct {
	server *server.Server
	conn   *nats.Conn
	ready  chan bool
}

// NewNATSServer creates a new embedded NATS server
func NewNATSServer(port int, clusterPort int) (*NATSServer, error) {
	// Get hostname for cluster identification
	hostname, err := os.Hostname()
	if err != nil {
		hostname = fmt.Sprintf("nats-server-%d", port)
	}

	// Create server options
	opts := &server.Options{
		Host:           "0.0.0.0",
		Port:           port,
		ServerName:     hostname,
		NoLog:          false,
		NoSigs:         true,
		MaxControlLine: 4096,
		JetStream:      true,
		// Cluster: server.ClusterOpts{
		// 	Name: "go-embed-nats-cluster",
		// 	Host: "0.0.0.0",
		// 	Port: clusterPort,
		// 	// Enable auto-discovery
		// 	NoAdvertise: false,
		// },
		Trace: false,
		Debug: false,
	}

	// Add cluster discovery via DNS if in Kubernetes-like environment
	clusterSeeds := os.Getenv("CLUSTER_SEEDS")
	if clusterSeeds != "" {
		opts.Cluster = server.ClusterOpts{
			Name:        "go-embed-nats-cluster",
			Host:        "0.0.0.0",
			Port:        clusterPort,
			NoAdvertise: false,
		}
		opts.Routes = server.RoutesFromStr(clusterSeeds)
	} else {
		// Disable clustering completely when no seeds are provided
		opts.Cluster.Port = 0 // Disable cluster port
	}

	// Create the server
	ns, err := server.NewServer(opts)
	if err != nil {
		return nil, fmt.Errorf("failed to create NATS server: %w", err)
	}

	// Configure logging
	ns.ConfigureLogger()

	return &NATSServer{server: ns, ready: make(chan bool, 1)}, nil
}

// Ready returns the channel that signals when the NATS server is ready
func (n *NATSServer) Ready() <-chan bool {
	return n.ready
}

// Start starts the NATS server and creates a client connection
func (n *NATSServer) Start() error {
	go n.server.Start()

	// Wait for server to be ready
	if !n.server.ReadyForConnections(10 * time.Second) {
		return fmt.Errorf("nats server failed to start")
	}

	log.Printf("NATS server listening on %s", n.server.ClientURL())
	if n.server.ClusterAddr() != nil {
		log.Printf("NATS cluster listening on %s", n.server.ClusterAddr())
	}

	// Create a client connection to the server
	var err error
	n.conn, err = nats.Connect(n.ClientURL())
	if err != nil {
		return fmt.Errorf("failed to connect to NATS server: %w", err)
	}
	log.Printf("Connected to NATS server at %s", n.conn.ConnectedUrl())

	n.ready <- true
	close(n.ready)

	return nil
}

// Stop stops the NATS server and closes the client connection
func (n *NATSServer) Stop() {
	if n.conn != nil {
		n.conn.Close()
	}
	n.server.Shutdown()
	log.Println("NATS server stopped")
}

// Shutdown gracefully shuts down the NATS server with a timeout context
func (n *NATSServer) Shutdown(ctx context.Context) error {
	// Create a channel to signal completion
	done := make(chan struct{})

	go func() {
		// Use the existing Stop method to handle shutdown
		n.Stop()
		close(done)
	}()

	// Wait for either shutdown to complete or context to timeout
	select {
	case <-done:
		return nil
	case <-ctx.Done():
		// Context expired, but the shutdown process will continue in the background
		// We just return the context error to indicate the timeout
		log.Println("NATS server shutdown timed out, continuing in background")
		return ctx.Err()
	}
}

// ClientURL returns the URL clients should use to connect
func (n *NATSServer) ClientURL() string {
	return n.server.ClientURL()
}

// GetConnection returns the NATS connection
func (n *NATSServer) GetConnection() *nats.Conn {
	return n.conn
}

// GetPort returns the port the server is listening on
func (n *NATSServer) GetPort() int {
	return n.server.Addr().(*net.TCPAddr).Port
}

// GetClusterPort returns the cluster port
func (n *NATSServer) GetClusterPort() int {
	if n.server.ClusterAddr() == nil {
		return 0
	}
	return n.server.ClusterAddr().Port
}

// GetNATSPort returns the NATS client port from environment or default
func GetNATSPort() int {
	portStr := os.Getenv("NATS_PORT")
	if portStr == "" {
		return 4222 // Default NATS port
	}
	port, err := strconv.Atoi(portStr)
	if err != nil {
		return 4222
	}
	return port
}

// GetNATSClusterPort returns the NATS cluster port from environment or default
func GetNATSClusterPort() int {
	portStr := os.Getenv("NATS_CLUSTER_PORT")
	if portStr == "" {
		return 6222 // Default NATS cluster port
	}
	port, err := strconv.Atoi(portStr)
	if err != nil {
		return 6222
	}
	return port
}

// GetClusterSeeds returns the cluster seeds from environment
func GetClusterSeeds() string {
	return os.Getenv("CLUSTER_SEEDS")
}
