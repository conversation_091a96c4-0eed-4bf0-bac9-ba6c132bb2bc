package main

import (
	"log"
	"os"

	"github.com/sourcegraph/go-embed-nats/internal/client/subscriber"
)

func main() {
	instanceID, err := os.Hostname()
	if err != nil {
		instanceID = "unknown"
	}
	subscriberErr := subscriber.SafeSubscribe("External-"+instanceID, "nats://app:4222")
	if subscriberErr != nil {
		log.Printf("Failed to start subscriber: %v", subscriberErr)
	}
	// Keep the application running
	select {}
}
