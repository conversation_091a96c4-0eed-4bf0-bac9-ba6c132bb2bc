version: '3'

services:
  
  app:
    build:
      context: .
    deploy:
      replicas: 3
    environment:
      - NATS_PORT=4222
      - NATS_CLUSTER_PORT=6222
      - HTTP_PORT=8080
      # First instance will be the seed for others
      - CLUSTER_SEEDS=nats://app:6222
    # ports:
    #   # Expose ports from the first replica
    #   - "4222:4222"
    #   - "8080:8080"
    networks:
      - nats-cluster
  curl:
    image: alpine:latest
    command: sleep infinity
    volumes:
      - ./:/app
    working_dir: /app
    networks:
      - nats-cluster
    # Install curl when container starts
    entrypoint: >
      sh -c "apk add --no-cache curl && sleep infinity"
  # subscriber:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile.sub
  #   networks:
  #     - nats-cluster
  #   depends_on:
  #     - app
  go:
    image: golang:1.23.0-alpine
    working_dir: /app
    volumes:
      - ./:/app
    networks:
      - nats-cluster
networks:
  nats-cluster:
    driver: bridge
