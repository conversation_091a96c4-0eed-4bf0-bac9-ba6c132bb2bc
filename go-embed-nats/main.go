package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/sourcegraph/go-embed-nats/internal/client/subscriber"
	"github.com/sourcegraph/go-embed-nats/internal/server"
)

func main() {
	// Create a context that we can cancel
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Create a channel to listen for errors from our servers
	errCh := make(chan error, 2)

	// Create a channel to listen for OS signals
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// Get configuration from environment
	natsPort := server.GetNATSPort()
	natsClusterPort := server.GetNATSClusterPort()
	httpPort := server.GetHTTPPort()

	// Get instance ID (hostname or environment variable)
	instanceID := os.Getenv("INSTANCE_ID")
	if instanceID == "" {
		var err error
		instanceID, err = os.Hostname()
		if err != nil {
			instanceID = fmt.Sprintf("unknown-%d", natsPort)
		}
	}

	log.Printf("Starting application with Instance ID: %s", instanceID)

	// Start the embedded NATS server
	natsServer, err := server.NewNATSServer(natsPort, natsClusterPort)
	if err != nil {
		log.Fatalf("Failed to create NATS server: %v", err)
	}

	go func() {
		if err := natsServer.Start(); err != nil {
			errCh <- fmt.Errorf("Failed to start NATS server: %w", err)
		}
	}()

	<-natsServer.Ready()

	// Start the HTTP server using the NATS connection
	httpServer, err := server.NewHTTPServer(httpPort, natsServer.GetConnection())
	if err != nil {
		log.Fatalf("Failed to create HTTP server: %v", err)
	}

	// Start HTTP server in a goroutine
	go func() {
		if err := httpServer.Start(); err != nil {
			errCh <- fmt.Errorf("Failed to start HTTP server: %w", err)
		}
	}()

	// Setup a simple subscriber to demonstrate message reception
	// natsServer.GetConnection().Subscribe("demo.>", func(msg *nats.Msg) {
	// 	log.Printf("[Instance: %s] Received message on %s: %s",
	// 		instanceID, msg.Subject, string(msg.Data))
	// })

	// Setup a subscriber with failsafe-go retry logic
	go func() {
		subscriberErr := subscriber.SafeSubscribe("Internal", natsServer.ClientURL())
		if subscriberErr != nil {
			log.Printf("Failed to start subscriber: %v", subscriberErr)
		}
	}()

	// Wait for interrupt signal

	// Wait for shutdown signal or error
	select {
	case <-sigCh:
		log.Println("Received shutdown signal")
	case err := <-errCh:
		log.Printf("Error: %v", err)
	case <-ctx.Done():
		log.Println("Context canceled")
	}

	// Create a timeout context for graceful shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer shutdownCancel()

	// Gracefully shutdown HTTP server
	log.Println("Shutting down HTTP server...")
	if err := httpServer.Shutdown(shutdownCtx); err != nil {
		log.Printf("HTTP server shutdown error: %v", err)
	}

	// Gracefully shutdown NATS server
	log.Printf("[Instance: %s] Shutting down...", instanceID)
	if err := natsServer.Shutdown(shutdownCtx); err != nil {
		log.Printf("NATS server shutdown error: %v", err)
	}

	log.Println("Shutdown complete")
}
