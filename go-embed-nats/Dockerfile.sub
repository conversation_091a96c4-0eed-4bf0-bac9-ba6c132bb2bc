FROM golang:1.23.0-alpine AS builder

WORKDIR /app

# Copy go mod and sum files
COPY go.mod ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build  -o /go-embed-nats ./cmd/suscriber/main.go

# Use a small image for the final stage
FROM alpine:latest

WORKDIR /

# Copy the binary from the builder stage
COPY --from=builder /go-embed-nats /go-embed-nats

# Expose ports
EXPOSE 4222 6222 8080

# Run the application
ENTRYPOINT ["/go-embed-nats"]
