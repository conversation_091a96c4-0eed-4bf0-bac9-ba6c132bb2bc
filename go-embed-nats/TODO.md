

## Server interface


```go
type Server interface {
    // Start initiates the server and begins accepting connections
    Start() error
    
    // Stop immediately stops the server (may interrupt connections)
    Stop()
    
    // Shutdown gracefully shuts down the server with a timeout context
    Shutdown(ctx context.Context) error
    
    // Ready returns a channel that signals when the server is ready
    Ready() <-chan bool
    
    // IsRunning returns whether the server is currently running
    IsRunning() bool
    
    // Address returns the server's listening address
    Address() string
}

```