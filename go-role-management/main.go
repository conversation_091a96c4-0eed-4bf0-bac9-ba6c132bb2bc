package main

import (
	"encoding/json"
	"fmt"
)

// <PERSON><PERSON><PERSON> holds the entire permission structure
type Authz struct {
	Services map[string]Service `json:"services"`
	Roles    map[string]Role    `json:"roles"`
	Scopes   map[string]Scope   `json:"scopes"`
}

// Service represents a microservice with modules
type Service struct {
	ID      string            `json:"id"`
	Modules map[string]Module `json:"modules"`
}

// Modu<PERSON> represents a module with permissions
type Module struct {
	ID          string          `json:"id"`
	Permissions map[string]bool `json:"permissions"`
}

// Role represents a role with permissions and optional inheritance
type Role struct {
	ID          string   `json:"id"`
	Inherits    []string `json:"inherits"`
	Permissions []string `json:"permissions"`
	Scopes      []string `json:"scopes"`
}

type Scope struct {
	ID      string              `json:"id"`
	Modules map[string][]string `json:"modules"`
}

// ScopeService represents a service with its allowed actions per module
type ScopeService struct {
	ID      string   `json:"id"`
	Module  string   `json:"module"`
	Actions []string `json:"actions"`
}

// ListScopeServices returns all services and their allowed actions from a scope definition
func (sd *Scope) ListScopeServices() []ScopeService {
	services := make([]ScopeService, 0)
	for module, actions := range sd.Modules {
		services = append(services, ScopeService{
			ID:      sd.ID,
			Module:  module,
			Actions: actions,
		})
	}
	return services
}

// ListModules returns a slice of all modules in the service
func (s *Service) ListModules() []Module {
	modules := make([]Module, 0, len(s.Modules))
	for moduleID, module := range s.Modules {
		module.ID = moduleID
		modules = append(modules, module)
	}
	return modules
}

// HasPermission checks if a role has a specific permission in a given service/module
func (config *Authz) HasPermission(service, role, permission string) bool {
	roleData, exists := config.Roles[role]
	if !exists {
		fmt.Println("Role does not exist:", role)
		return false
	}

	// Direct permission check
	for _, perm := range roleData.Permissions {
		if perm == permission {
			return true
		}
	}

	// Check inherited roles
	for _, inheritedRole := range roleData.Inherits {
		if config.HasPermission(service, inheritedRole, permission) {
			return true
		}
	}

	return false
}

// HasScope checks if a role has access to a specific scope
func (config *Authz) HasScope(service, role, module, action string) bool {
	roleData, exists := config.Roles[role]
	if !exists {
		fmt.Println("Role does not exist:", role)
		return false
	}

	// Check role's scopes
	for _, scope := range roleData.Scopes {
		if scopeData, ok := config.Scopes[service]; ok && scope != "" {
			if actions, exists := scopeData.Modules[module]; exists {
				for _, allowedAction := range actions {
					if allowedAction == action {
						return true
					}
				}
			}
		}
	}

	// Check inherited roles
	for _, inheritedRole := range roleData.Inherits {
		if config.HasScope(service, inheritedRole, module, action) {
			return true
		}
	}

	return false
}

// ListServices returns a slice of all services with their complete information
func (config *Authz) ListServices() []Service {
	services := make([]Service, 0, len(config.Services))
	for serviceID, service := range config.Services {
		service.ID = serviceID
		services = append(services, service)
	}
	return services
}

// ListRoles returns a slice of all roles with their complete information
func (config *Authz) ListRoles() []Role {
	roles := make([]Role, 0, len(config.Roles))
	for roleID, role := range config.Roles {
		role.ID = roleID
		roles = append(roles, role)
	}
	return roles
}

// ListScopes returns all scope definitions with their complete information
func (config *Authz) ListScopes() []Scope {
	scopeDefs := make([]Scope, 0, len(config.Scopes))
	for scopeID, scope := range config.Scopes {
		scopeDefs = append(scopeDefs, Scope{
			ID:      scopeID,
			Modules: scope.Modules,
		})
	}
	return scopeDefs
}

// Sample JSON data
var jsonData = `
{
  "services": {
    "user-service": {
      "modules": {
        "user-management": {
          "permissions": {
            "user.read": true,
            "user.write": true,
            "user.delete": false
          }
        }
      }
    },
    "order-service": {
      "modules": {
        "order-management": {
          "permissions": {
            "order.read": true,
            "order.write": true,
            "order.process": true,
            "order.cancel": false
          }
        }
      }
    }
  },
  "roles": {
    "admin": {
      "inherits": ["editor"],
      "permissions": [
        "user.write",
        "user.delete",
        "order.process"
      ],
      "scopes": [
        "order-service.order-management"
      ]
    },
    "editor": {
      "inherits": ["viewer"],
      "permissions": [
        "user.read",
        "user.write",
        "order.read",
        "order.write"
      ],
      "scopes": [
        "user-service.user-management"
      ]
    },
    "viewer": {
      "permissions": [
        "user.read",
        "order.read"
      ],
      "scopes": []
    }
  },
  "scopes": {
    "user-service": {
      "modules": {
        "user-management": ["read", "write"]
      }
    },
    "order-service": {
      "modules": {
        "order-management": ["read", "write", "process"]
      }
    }
  }
}`

func main() {
	// Load JSON config into struct
	var config Authz
	err := json.Unmarshal([]byte(jsonData), &config)
	if err != nil {
		fmt.Println("Error loading JSON:", err)
		return
	}

	// Test permission checking
	fmt.Println("Can admin process orders?", config.HasPermission("order-service", "admin", "order.process")) // ✅ true
	fmt.Println("Can editor cancel orders?", config.HasPermission("order-service", "editor", "order.cancel")) // ❌ false
	fmt.Println("Can viewer read orders?", config.HasPermission("order-service", "viewer", "order.read"))     // ✅ true
	fmt.Println("Can admin delete users?", config.HasPermission("user-service", "admin", "user.delete"))      // ✅ true
	fmt.Println("Can viewer write orders?", config.HasPermission("order-service", "viewer", "order.write"))   // ❌ false

	// Test scope checking
	fmt.Println("Does admin have scope 'process' for order-service.order-management?", config.HasScope("order-service", "admin", "order-management", "process")) // ✅ true
	fmt.Println("Does viewer have scope 'read' for user-service.user-management?", config.HasScope("user-service", "viewer", "user-management", "read"))         // ❌ false
	fmt.Println("Does editor have scope 'write' for user-service.user-management?", config.HasScope("user-service", "editor", "user-management", "write"))       // ✅ true

	// Test getting all modules for each service
	fmt.Println("\nAll Modules by Service:")
	services := config.ListServices()
	for _, service := range services {
		fmt.Printf("Service: %s\n", service.ID)
		modules := service.ListModules()
		for _, module := range modules {
			fmt.Printf("  - Module ID: %s\n    Permissions: %v\n",
				module.ID,
				module.Permissions)
		}
	}

	// Test getting all roles
	fmt.Println("\nAll Roles:")
	roles := config.ListRoles()
	for _, role := range roles {
		fmt.Printf("- ID: %s\n  Inherits: %v\n  Permissions: %v\n  Scopes: %v\n",
			role.ID,
			role.Inherits,
			role.Permissions,
			role.Scopes)
	}

	// Test getting all scope services
	fmt.Println("\nAll Scope Services by Definition:")
	scopeDefs := config.ListScopes()
	for _, scopeDef := range scopeDefs {
		fmt.Printf("Scope: %s\n", scopeDef.ID)
		services := scopeDef.ListScopeServices()
		for _, service := range services {
			fmt.Printf("  - Module: %s\n    Actions: %v\n",
				service.Module,
				service.Actions)
		}
	}

}
