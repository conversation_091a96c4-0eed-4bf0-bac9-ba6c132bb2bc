package calculator_test

import (
	"log"
	"os"
	"testing"

	"go-tdd/features/calculator"
)

func init() {
	log.Println("Init setup.")
}

func TestMain(m *testing.M) {
	// setup statements
	setup()

	// run tests
	e := m.Run()

	// cleanup statements
	teardown()

	// report the exit code
	os.Exit(e)
}

func setup() { log.Println("Setting up.") }

func teardown() { log.Println("Tearing down.") }

func TestAdd(t *testing.T) {
	defer func() { log.Println("Deferred tearing down.") }()
	// Arrange
	e := calculator.NewEngine()

	actAssert := func(x, y, want float64) {
		// Act
		got := e.Add(x, y)
		// Assert
		if got != want {
			t.Errorf("Add(%.2f,%.2f) incorrect, got: %.2f, want:      %.2f", x, y, got, want)
		}
	}

	t.Run("positive input", func(t *testing.T) {
		// Act
		x, y := 2.5, 3.5
		want := 6.0
		actAssert(x, y, want)
	})

	t.Run("negative input", func(t *testing.T) {
		// Act
		x, y := 2.5, 3.5
		want := 6.0
		actAssert(x, y, want)
	})
}

func BenchmarkAdd(b *testing.B) {
	e := calculator.NewEngine()

	// run the Add function b.N times
	x, y := 2.0, 3.0
	for i := 0; i < b.N; i++ {
		e.Add(x, y)
	}
}
