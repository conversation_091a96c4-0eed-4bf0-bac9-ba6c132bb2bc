package main

import (
	"bufio"
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"strings"

	"flowgo/pipeline"
)

func main() {
	ctx := context.Background()
	errCh := make(chan error, 10)
	defer close(errCh)

	// Read file line by line into channel
	lines := make(chan string)
	go func() {
		defer close(lines)
		file, err := os.Open("example.csv")
		if err != nil {
			errCh <- err
			return
		}
		defer file.Close()
		scanner := bufio.NewScanner(file)
		for scanner.Scan() {
			lines <- scanner.Text()
		}
	}()

	// Transform: parse CSV rows
	records := pipeline.Parallel(ctx, lines, func(line string) ([]string, error) {
		r := csv.NewReader(strings.NewReader(line))
		return r.Read()
	}, errCh, 4)

	// Transform: filter out invalid rows
	filtered := pipeline.Parallel(ctx, records, func(row []string) ([]string, error) {
		if len(row) < 2 {
			return nil, fmt.Errorf("too short")
		}
		return row, nil
	}, errCh, 4)

	// Transform: uppercase first column
	upper := pipeline.Parallel(ctx, filtered, func(row []string) ([]string, error) {
		row[0] = strings.ToUpper(row[0])
		return row, nil
	}, errCh, 4)

	// Consume
	for row := range upper {
		fmt.Println("Processed:", row)
	}

	for len(errCh) > 0 {
		fmt.Println("Error:", <-errCh)
	}
}
