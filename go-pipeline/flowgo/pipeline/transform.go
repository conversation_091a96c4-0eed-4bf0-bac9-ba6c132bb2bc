package pipeline

import (
	"context"
	"fmt"
	"sync"
	"time"
)

func Serial[In, Out any](
	ctx context.Context,
	in <-chan In,
	fn func(In) (Out, error),
	errCh chan<- error,
) <-chan Out {
	return TransformWorkers(ctx, in, fn, errCh, 1)
}

func Parallel[In, Out any](
	ctx context.Context,
	in <-chan In,
	fn func(In) (Out, error),
	errCh chan<- error,
	workers int,
) <-chan Out {
	return TransformWorkers(ctx, in, fn, errCh, workers)
}

func TransformWorkers[In, Out any](
	ctx context.Context,
	in <-chan In,
	fn func(In) (Out, error),
	errCh chan<- error,
	workers int,
) <-chan Out {
	out := make(chan Out)

	if workers <= 0 {
		go func() {
			errCh <- fmt.Errorf("invalid worker count: %d", workers)
			close(out)
		}()
		return out
	}

	var wg sync.WaitGroup
	wg.Add(workers)

	for w := 0; w < workers; w++ {
		go func() {
			defer wg.Done()
			for item := range in {
				if ctx.Err() != nil {
					return
				}
				val, err := fn(item)
				if err != nil {
					errCh <- err
					continue
				}
				out <- val
			}
		}()
	}

	go func() {
		wg.Wait()
		close(out)
	}()

	return out
}

func RetryWithBackoff[T any](
	ctx context.Context,
	fn func() (T, error),
	maxRetries int,
	baseDelay time.Duration,
) (T, error) {
	var lastErr error
	var zero T

	for attempt := 0; attempt <= maxRetries; attempt++ {
		val, err := fn()
		if err == nil {
			return val, nil
		}
		lastErr = err

		select {
		case <-ctx.Done():
			return zero, ctx.Err()
		case <-time.After(baseDelay * (1 << attempt)):
		}
	}
	return zero, fmt.Errorf("all retries failed: %w", lastErr)
}
