package pipeline

import (
	"context"
)

func Dedup[T comparable](ctx context.Context, in <-chan T) <-chan T {
	out := make(chan T)
	go func() {
		defer close(out)
		seen := make(map[T]struct{})
		for v := range in {
			if _, ok := seen[v]; !ok {
				seen[v] = struct{}{}
				out <- v
			}
		}
	}()
	return out
}

func Join[K comparable, A, B any](
	ctx context.Context,
	left <-chan A,
	right <-chan B,
	keyA func(A) K,
	keyB func(B) K,
	combine func(A, B) any,
) <-chan any {
	out := make(chan any)
	go func() {
		defer close(out)
		rightMap := make(map[K]B)
		for b := range right {
			rightMap[keyB(b)] = b
		}
		for a := range left {
			k := keyA(a)
			if b, ok := rightMap[k]; ok {
				out <- combine(a, b)
			}
		}
	}()
	return out
}
