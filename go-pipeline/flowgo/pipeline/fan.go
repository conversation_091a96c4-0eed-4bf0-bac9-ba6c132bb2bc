package pipeline

import (
	"context"
	"sync"
)

func FanOut[T any](ctx context.Context, in <-chan T, n int) []<-chan T {
	if n <= 0 {
		panic("fan-out count must be > 0")
	}

	outs := make([]chan T, n)
	for i := 0; i < n; i++ {
		outs[i] = make(chan T)
	}

	go func() {
		defer func() {
			for _, ch := range outs {
				close(ch)
			}
		}()
		i := 0
		for item := range in {
			if ctx.Err() != nil {
				return
			}
			outs[i%len(outs)] <- item
			i++
		}
	}()

	result := make([]<-chan T, n)
	for i := range outs {
		result[i] = outs[i]
	}
	return result
}

func FanIn[T any](ctx context.Context, chans ...<-chan T) <-chan T {
	out := make(chan T)
	var wg sync.WaitGroup

	wg.Add(len(chans))
	for _, ch := range chans {
		go func(c <-chan T) {
			defer wg.Done()
			for v := range c {
				select {
				case <-ctx.Done():
					return
				case out <- v:
				}
			}
		}(ch)
	}

	go func() {
		wg.Wait()
		close(out)
	}()

	return out
}
