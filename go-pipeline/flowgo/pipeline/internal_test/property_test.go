package internal_test

import (
	"context"
	"testing"

	"flowgo/pipeline"

	"github.com/stretchr/testify/assert"
)

func TestDedup(t *testing.T) {
	ctx := context.Background()
	in := make(chan int)
	go func() {
		defer close(in)
		in <- 1
		in <- 2
		in <- 2
		in <- 3
		in <- 1
	}()

	out := pipeline.Dedup(ctx, in)
	results := []int{}
	for v := range out {
		results = append(results, v)
	}

	assert.ElementsMatch(t, []int{1, 2, 3}, results)
}
