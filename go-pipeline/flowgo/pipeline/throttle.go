package pipeline

import (
	"context"
	"fmt"
	"sync"
	"time"
)

func RateLimiter[T any](ctx context.Context, in <-chan T, rate time.Duration) <-chan T {
	out := make(chan T)

	go func() {
		defer close(out)
		ticker := time.NewTicker(rate)
		defer ticker.Stop()

		for item := range in {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				out <- item
			}
		}
	}()
	return out
}

func ThrottledMap[In, Out any](
	ctx context.Context,
	in <-chan In,
	fn func(In) (Out, error),
	delay time.Duration,
	errCh chan<- error,
	workers int,
) <-chan Out {
	out := make(chan Out)

	if workers <= 0 {
		go func() {
			errCh <- fmt.Errorf("invalid worker count: %d", workers)
			close(out)
		}()
		return out
	}

	var wg sync.WaitGroup
	wg.Add(workers)

	for w := 0; w < workers; w++ {
		go func() {
			defer wg.Done()
			t := time.NewTicker(delay)
			defer t.Stop()

			for item := range in {
				select {
				case <-ctx.Done():
					return
				case <-t.C:
					val, err := fn(item)
					if err != nil {
						errCh <- err
						continue
					}
					out <- val
				}
			}
		}()
	}

	go func() {
		wg.Wait()
		close(out)
	}()
	return out
}
