package pipeline

import (
	"context"
	"time"
)

func Batch[T any](ctx context.Context, in <-chan T, size int) <-chan []T {
	out := make(chan []T)
	if size <= 0 {
		panic("batch size must be > 0")
	}

	go func() {
		defer close(out)
		batch := make([]T, 0, size)

		for {
			select {
			case <-ctx.Done():
				return
			case item, ok := <-in:
				if !ok {
					if len(batch) > 0 {
						out <- batch
					}
					return
				}
				batch = append(batch, item)
				if len(batch) == size {
					out <- batch
					batch = make([]T, 0, size)
				}
			}
		}
	}()
	return out
}

func WindowedBatch[T any](ctx context.Context, in <-chan T, size int, window time.Duration) <-chan []T {
	out := make(chan []T)
	go func() {
		defer close(out)
		batch := make([]T, 0, size)
		timer := time.NewTimer(window)
		defer timer.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case item, ok := <-in:
				if !ok {
					if len(batch) > 0 {
						out <- batch
					}
					return
				}
				batch = append(batch, item)
				if len(batch) == size {
					out <- batch
					batch = make([]T, 0, size)
					if !timer.Stop() {
						<-timer.C
					}
					timer.Reset(window)
				}
			case <-timer.C:
				if len(batch) > 0 {
					out <- batch
					batch = make([]T, 0, size)
				}
				timer.Reset(window)
			}
		}
	}()
	return out
}
