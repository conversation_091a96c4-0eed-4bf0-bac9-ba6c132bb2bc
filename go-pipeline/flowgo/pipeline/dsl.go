package pipeline

// import (
// 	"context"
// )

// type StepFunc[In, Out any] func(context.Context, <-chan In, chan<- error) <-chan Out

// type Pipeline[In, Out any] struct {
// 	steps []func(context.Context, <-chan any, chan<- error) <-chan any
// }

// func NewPipeline[In, Out any]() *Pipeline[In, Out] {
// 	return &Pipeline[In, Out]{}
// }

// func (p *Pipeline[In, Out]) AddStep(step StepFunc[T, A]) *Pipeline[In, Out] {
// 	adapted := func(ctx context.Context, in <-chan any, errCh chan<- error) <-chan any {
// 		typedIn := make(chan A)
// 		go func() {
// 			defer close(typedIn)
// 			for v := range in {
// 				typedIn <- v.(A)
// 			}
// 		}()
// 		typedOut := step(ctx, typedIn, errCh)
// 		adaptedOut := make(chan any)
// 		go func() {
// 			defer close(adaptedOut)
// 			for v := range typedOut {
// 				adaptedOut <- v
// 			}
// 		}()
// 		return adaptedOut
// 	}
// 	p.steps = append(p.steps, adapted)
// 	return p
// }

// func (p *Pipeline[In, Out]) Run(ctx context.Context, in <-chan In, errCh chan<- error) <-chan Out {
// 	var current <-chan any = make(chan any)
// 	go func() {
// 		defer close(current.(chan any))
// 		for v := range in {
// 			current.(chan any) <- v
// 		}
// 	}()
// 	for _, step := range p.steps {
// 		current = step(ctx, current, errCh)
// 	}
// 	out := make(chan Out)
// 	go func() {
// 		defer close(out)
// 		for v := range current {
// 			out <- v.(Out)
// 		}
// 	}()
// 	return out
// }
