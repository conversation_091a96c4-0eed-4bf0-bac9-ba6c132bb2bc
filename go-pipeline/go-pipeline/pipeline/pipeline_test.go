package pipeline

import (
	"context"
	"testing"
	"time"
)

func TestRun(t *testing.T) {
	tests := []struct {
		name     string
		request  Request
		wantErr  bool
		verifyFn func(t *testing.T, req Request, resp Response)
	}{
		{
			name:    "valid request",
			request: Request{Email: "<EMAIL>", Password: "123"},
			wantErr: false,
			verifyFn: func(t *testing.T, req Request, resp Response) {
				if resp.Email != req.Email {
					t.Errorf("expected email %s, got %s", req.Email, resp.Email)
				}
			},
		},
		{
			name:    "invalid request",
			request: Request{Email: "", Password: ""},
			wantErr: true,
		},
		{
			name:    "duplicate email",
			request: Request{Email: "<EMAIL>", Password: "123"},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			input := make(chan Request, 1)
			input <- tt.request
			result, errCh := Run(context.Background(), input)

			select {
			case err := <-errCh:
				if (err != nil) != tt.wantErr {
					t.Errorf("expected error: %v, got: %v", tt.wantErr, err != nil)
				}
			case r, ok := <-result:
				if !ok {
					t.Errorf("expected result, got none")
				}

				if tt.verifyFn != nil {
					tt.verifyFn(t, tt.request, r)
				}
			case <-time.After(100 * time.Millisecond):
				t.Errorf("timeout")
			}
		})
	}
}
