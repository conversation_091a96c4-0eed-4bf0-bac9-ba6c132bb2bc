package pipeline

import (
	"context"
	"errors"
	"fmt"
)

type Request struct {
	Email    string
	Password string
}

type User struct {
	Email        string
	PasswordHash string
}

type Response struct {
	Email string
}

func Run(ctx context.Context, inputCh <-chan Request) (<-chan Response, <-chan error) {
	errCh := make(chan error, 1)

	reqCh := Pipe(ctx, inputCh, ValidateInput, errCh)
	reqCh = Pipe(ctx, reqCh, CheckExistingUser, errCh)
	userCh := Pipe(ctx, reqCh, SaveUser, errCh)
	respCh := Pipe(ctx, userCh, SendWelcomeEmail, errCh, WithMiddlewares(
		func(next StageFunc[User, Response]) StageFunc[User, Response] {
			return func(u User) (Response, error) {
				fmt.Printf("Sending welcome email to %s\n", u.Email)
				user, err := next(u)
				fmt.Printf("Welcome email sent to %s\n", u.Email)
				return user, err
			}
		},
	))

	return respCh, errCh
}

func ValidateInput(r Request) (Request, error) {
	if r.Email == "" || r.Password == "" {
		return r, errors.New("validation error: missing email or password")
	}
	return r, nil
}

func CheckExistingUser(r Request) (Request, error) {
	if r.Email == "<EMAIL>" {
		return r, errors.New("duplicate email: " + r.Email)
	}
	return r, nil
}

func SaveUser(r Request) (User, error) {
	return User{Email: r.Email}, nil
}

func SendWelcomeEmail(u User) (Response, error) {
	return Response{Email: u.Email}, nil
}
