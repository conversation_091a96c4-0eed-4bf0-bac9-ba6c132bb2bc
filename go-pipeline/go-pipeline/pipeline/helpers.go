package pipeline

import (
	"context"
	"fmt"
	"slices"
	"sync"
)

type Stage[In, Out any] interface {
	Process(In) (Out, error)
}

type StageFunc[In, Out any] func(In) (Out, error)

func (f StageFunc[I, O]) Process(input I) (O, error) {
	return f(input)
}

type MiddlewareFunc[In, Out any] func(StageFunc[In, Out]) StageFunc[In, Out]

type stageOptionsFunc[In, Out any] func(*stageOptions[In, Out])

type stageOptions[In, Out any] struct {
	workers     int
	middlewares []MiddlewareFunc[In, Out]
}

func WithMiddlewares[In, Out any](middlewares ...MiddlewareFunc[In, Out]) stageOptionsFunc[In, Out] {
	return func(opts *stageOptions[In, Out]) {
		opts.middlewares = middlewares
	}
}

func WithWorkers[In, Out any](workers int) stageOptionsFunc[In, Out] {
	return func(opts *stageOptions[In, Out]) {
		opts.workers = workers
	}
}

func Pipe[In, Out any](
	ctx context.Context,
	in <-chan In,
	fn StageFunc[In, Out],
	errCh chan<- error,
	optsFuncs ...stageOptionsFunc[In, Out],
) <-chan Out {
	out := make(chan Out)

	options := &stageOptions[In, Out]{
		workers: 1,
	}

	for _, optsFunc := range optsFuncs {
		optsFunc(options)
	}

	workers := options.workers

	if workers <= 0 {
		go func() {
			errCh <- fmt.Errorf("invalid worker count: %d", workers)
			close(out)
		}()
		return out
	}

	var wg sync.WaitGroup
	wg.Add(workers)

	for w := 0; w < workers; w++ {
		go func() {
			defer wg.Done()
			for item := range in {
				if ctx.Err() != nil {
					return
				}

				if len(options.middlewares) > 0 {
					middlewares := options.middlewares
					slices.Reverse(middlewares)
					for _, mw := range middlewares {
						fn = mw(fn)
					}
				}
				val, err := fn(item)
				if err != nil {
					errCh <- err
					continue
				}
				out <- val
			}
		}()
	}

	go func() {
		wg.Wait()
		close(out)
	}()

	return out
}
