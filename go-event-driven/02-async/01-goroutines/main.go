package main

import (
	"log"
	"time"
)

type User struct {
	Email string
}

type UserRepository interface {
	CreateUserAccount(u User) error
}

type NotificationsClient interface {
	SendNotification(u User) error
}

type NewsletterClient interface {
	AddToNewsletter(u User) error
}

type Handler struct {
	repository          UserRepository
	newsletterClient    NewsletterClient
	notificationsClient NotificationsClient
}

func NewHandler(
	repository UserRepository,
	newsletterClient NewsletterClient,
	notificationsClient NotificationsClient,
) Handler {
	return Handler{
		repository:          repository,
		newsletterClient:    newsletterClient,
		notificationsClient: notificationsClient,
	}
}

func (h Handler) SignUp(u User) error {
	if err := h.repository.CreateUserAccount(u); err != nil {
		return err
	}

	go func() {
		retry(u, h.newsletterClient.AddToNewsletter)
	}()
	go func() {
		retry(u, h.notificationsClient.SendNotification)
	}()

	return nil
}

func retry(u User, f func(u User) error) {
	for {
		err := f(u)
		if err != nil {
			log.Printf("failed execute action: %v", err)
			time.Sleep(100 * time.Millisecond)
			continue
		}
		break
	}
}
