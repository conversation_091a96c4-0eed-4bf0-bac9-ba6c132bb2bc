package main

import (
	"os"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill-redisstream/pkg/redisstream"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/redis/go-redis/v9"
)

func main() {
	logger := watermill.NewStdLogger(false, false)

	rdb := redis.NewClient(&redis.Options{
		Addr: os.Getenv("REDIS_ADDR"),
	})

	publisher, err := redisstream.NewPublisher(redisstream.PublisherConfig{
		Client: rdb,
	}, logger)

	topic := "progress"

	payload := [][]byte{
		[]byte("50"),
		[]byte("100"),
	}

	for _, p := range payload {
		msg := message.NewMessage(watermill.NewUUID(), p)
		publisher.Publish(topic, msg)
	}

	defer func() {
		if err := publisher.Close(); err != nil {
			panic(err)
		}
	}()

	if err != nil {
		panic(err)
	}
}
