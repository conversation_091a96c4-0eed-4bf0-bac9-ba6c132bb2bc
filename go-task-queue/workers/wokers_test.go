package workers

import (
	"context"
	"fmt"
	"log"
	"log/slog"
	"math/rand/v2"
	"os"
	"runtime"
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

type TestWorker struct {
	workerName string
	workerID   string
	value      string
	callback   func(ctx context.Context) error
}

func (tw *TestWorker) Work(ctx context.Context) error {
	return tw.callback(ctx)
}

func (tw *TestWorker) ID() string {
	return tw.workerID
}

func NewTestWorker(workerName string, workerID string, value string) *TestWorker {
	return &TestWorker{
		workerName: workerName,
		workerID:   workerID,
		value:      value,
	}
}

func TestWorkerPoolWithManyTasks(t *testing.T) {
	opts := &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}
	handler := slog.NewTextHandler(os.Stdout, opts)
	logger := slog.New(handler)
	slog.SetDefault(logger)

	tests := []struct {
		name       string
		numTasks   int
		numWorkers int
		timeout    time.Duration
	}{
		{"Small Task Count", 10, runtime.NumCPU(), 5 * time.Second},
		{"Medium Task Count", 100, runtime.NumCPU() * 2, 10 * time.Second},
		{"Large Task Count", 1000, runtime.NumCPU() * 4, 20 * time.Second},
		{"Very Large Task Count", 5000, runtime.NumCPU() * 30, 1 * time.Second},
		{"Ultra Large Task Count", 10000, runtime.NumCPU() * 60, 1 * time.Second},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			var since time.Duration

			// Reset the global counter for each test
			var submittedTasks atomic.Int64
			var submittedFailedTasks atomic.Int64
			var workerSuccessfulTasks atomic.Int64
			var workerFailedTasks atomic.Int64

			// Create a context with timeout
			ctx, cancel := context.WithTimeout(context.Background(), tt.timeout)
			defer cancel()

			// Create a worker pool with capacity based on number of workers
			poolCapacity := tt.numWorkers * 2
			workerPool := NewWorkerPool(poolCapacity)

			// Start the worker pool
			err := workerPool.Start(ctx, tt.numWorkers)
			if err != nil {
				t.Fatalf("Failed to start worker pool: %v", err)
			}

			// Create a wait group to track task completion
			var taskCompletionWG sync.WaitGroup

			startTaskSubmission := time.Now()
			// Submit tasks to the worker pool
			for i := 1; i <= tt.numTasks; i++ {
				worker := NewTestWorker("test", fmt.Sprintf("%d", i), fmt.Sprintf("job%d", i))

				worker.callback = func(ctx context.Context) error {
					defer taskCompletionWG.Done()
					submittedTasks.Add(1)
					// time.Sleep(1 * time.Millisecond) // Simulate work
					if rand.Float64() <= 0.4 {
						workerFailedTasks.Add(1)
						return fmt.Errorf("task %d failed", i)
					}
					workerSuccessfulTasks.Add(1)
					return nil
				}

				taskCompletionWG.Add(1) // Add to WaitGroup before submission
				err := workerPool.Submit(worker, false)
				if err != nil {
					t.Logf("Failed to submit task %d: %v", i, err)
					taskCompletionWG.Done() // Release the wait if submission fails
					submittedFailedTasks.Add(1)
				}
			}

			// Wait for tasks to complete with timeout
			completedCh := make(chan struct{})
			go func() {
				taskCompletionWG.Wait()
				completedCh <- struct{}{}
				close(completedCh)
				since = time.Since(startTaskSubmission)
			}()

			// Wait for completion or timeout
			select {
			case <-completedCh:
				t.Logf("All tasks completed successfully")

			case <-time.After(tt.timeout):
				t.Logf("Timed out waiting for tasks to complete")
			}

			// Stop the worker pool

			stopCtx, stopCancel := context.WithTimeout(context.Background(), tt.timeout)
			defer stopCancel()

			if err := workerPool.Stop(stopCtx); err != nil {
				t.Errorf("Worker pool shutdown failed: %v", err)
			}

			testTotalFaileddTasks := int(submittedFailedTasks.Load())
			testTotalSubmittedTasks := int(submittedTasks.Load())
			testWorkerSuccessfulTasks := int(workerSuccessfulTasks.Load())
			testWorkerFaileddTasks := int(workerFailedTasks.Load())

			// Verify results
			t.Logf("Test Name: %s", tt.name)
			t.Logf("Test Num Tasks: %d", tt.numTasks)
			t.Logf("Test Task completed time: %v", since)
			t.Logf("Test Executed Tasks: %d", testTotalSubmittedTasks)
			t.Logf("Test Failed Submitted Tasks: %d", testTotalFaileddTasks)
			t.Logf("Worker Successfull Submitted Tasks: %d", testWorkerSuccessfulTasks)
			t.Logf("Worker Failed Submitted Tasks: %d", testWorkerFaileddTasks)
			t.Logf("Worker Submitted Tasks: %d", workerPool.SubmittedTasksCount())
			t.Logf("Worker Pending Tasks: %d", workerPool.PendingTasksCount())

			testTotalProcessedTasks := (testWorkerSuccessfulTasks + testWorkerFaileddTasks)

			// Verify all tasks were processed
			if testTotalProcessedTasks != testTotalSubmittedTasks {
				t.Errorf("Expected %d processed tasks, got %d", tt.numTasks, workerPool.ProcessedTasksCount())
			}

			if int(workerSuccessfulTasks.Load()) != workerPool.SuccessfulTasksCount() {
				t.Errorf("Expected %d successfull processed tasks, got %d", tt.numTasks, workerPool.SuccessfulTasksCount())
			}

			if int(workerFailedTasks.Load()) != workerPool.FailedTasksCount() {
				t.Errorf("Expected %d failed processed tasks, got %d", tt.numTasks, workerPool.FailedTasksCount())
			}
		})
	}
}

func BenchmarkWorkerPool(b *testing.B) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	numWorkers := 20
	poolCapacity := numWorkers * 2
	workerPool := NewWorkerPool(poolCapacity)

	err := workerPool.Start(ctx, numWorkers)
	if err != nil {
		b.Fatalf("Failed to start worker pool: %v", err)
	}

	b.ResetTimer()

	// Run the benchmark
	b.RunParallel(func(pb *testing.PB) {
		counter := 0
		for pb.Next() {
			counter++
			worker := NewTestWorker("bench", fmt.Sprintf("%d", counter), fmt.Sprintf("bench-job%d", counter))
			_ = workerPool.Submit(worker, false)
		}
	})

	b.StopTimer()

	stopCtx, stopCancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer stopCancel()

	if err := workerPool.Stop(stopCtx); err != nil {
		b.Errorf("Worker pool shutdown failed: %v", err)
	}

	log.Printf("Benchmark completed. Tasks processed: %d", workerPool.ProcessedTasksCount())
}
