package workers

import (
	"encoding/json"
	"fmt"
	"reflect"
	"sync"
)

// WorkerTypeRegistry maintains a registry of worker types that can be deserialized
type WorkerTypeRegistry struct {
	types sync.Map
}

// Global registry instance
var globalRegistry = NewWorkerTypeRegistry()

// NewWorkerTypeRegistry creates a new worker type registry
func NewWorkerTypeRegistry() *WorkerTypeRegistry {
	return &WorkerTypeRegistry{
		types: sync.Map{},
	}
}

// Register registers a worker type with the registry
func (r *WorkerTypeRegistry) Register(kind Worker) {
	t := reflect.TypeOf(kind).Elem()
	typeName := getFullTypeName(t)
	r.types.Store(typeName, t)
}

// Register registers a worker type with the global registry
func Register(kind Worker) {
	globalRegistry.Register(kind)
}

// getFullTypeName returns the full type name including package path
func getFullTypeName(t reflect.Type) string {
	if t.PkgPath() == "" {
		return t.Name() // Built-in types don't have a package path
	}
	return t.PkgPath() + "." + t.Name()
}

// SerializedTask represents the serialized form of a worker
type SerializedTask struct {
	Type string          `json:"type"`
	Data json.RawMessage `json:"data"`
}

// SerializeTask serializes any Worker to a byte slice
func SerializeTask(worker Worker) ([]byte, error) {
	// Get the full type name using reflection
	t := reflect.TypeOf(worker).Elem()
	typeName := getFullTypeName(t)

	// First serialize the worker data
	workerData, err := json.Marshal(worker)
	if err != nil {
		return nil, fmt.Errorf("failed to serialize worker data: %w", err)
	}

	// Create the wrapper with type information
	wrapper := SerializedTask{
		Type: typeName,
		Data: workerData,
	}

	// Serialize the wrapper
	wrapperData, err := json.Marshal(wrapper)
	if err != nil {
		return nil, fmt.Errorf("failed to serialize worker wrapper: %w", err)
	}

	return wrapperData, nil
}

// DeserializeWorker deserializes a byte slice back to a Worker
func DeserializeWorker(serialized []byte) (Worker, error) {
	return globalRegistry.DeserializeWorker(serialized)
}

// DeserializeWorker deserializes a byte slice back to a Worker using the registry
func (r *WorkerTypeRegistry) DeserializeWorker(serialized []byte) (Worker, error) {
	var wrapper SerializedTask
	if err := json.Unmarshal(serialized, &wrapper); err != nil {
		return nil, fmt.Errorf("failed to deserialize worker wrapper: %w", err)
	}

	// Look up the type in the registry
	workerTypeValue, exists := r.types.Load(wrapper.Type)
	if !exists {
		return nil, fmt.Errorf("unknown worker type: %s", wrapper.Type)
	}

	// Convert the value to reflect.Type
	workerType, ok := workerTypeValue.(reflect.Type)
	if !ok {
		return nil, fmt.Errorf("invalid type stored in registry for: %s", wrapper.Type)
	}

	// Create a new instance of the worker type
	workerPtr := reflect.New(workerType).Interface()

	// Deserialize the worker data into the new instance
	if err := json.Unmarshal(wrapper.Data, workerPtr); err != nil {
		return nil, fmt.Errorf("failed to deserialize worker data: %w", err)
	}

	// Convert to Worker interface
	worker, ok := workerPtr.(Worker)
	if !ok {
		return nil, fmt.Errorf("deserialized object is not a Worker: %T", workerPtr)
	}

	return worker, nil
}
