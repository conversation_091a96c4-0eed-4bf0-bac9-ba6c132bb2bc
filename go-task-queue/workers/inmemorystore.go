package workers

import (
	"context"
	"fmt"
	"log/slog"
	"math/rand/v2"
	"time"
)

func NewInMemoryStore() *InMemoryStore {
	maxTasks := 2000
	notificationPeriod := 1 * time.Second
	batchSize := 1
	return &InMemoryStore{
		tasks:              make([]Worker, 0, maxTasks),
		maxTasks:           maxTasks,
		notifications:      make(chan struct{}, 1),
		notificationPeriod: notificationPeriod,
		batchSize:          batchSize,
	}
}

type InMemoryStore struct {
	tasks              []Worker
	maxTasks           int
	notifications      chan struct{}
	notificationPeriod time.Duration
	batchSize          int
}

var _ StoreProcessor = (*InMemoryStore)(nil)

func (s *InMemoryStore) PendingTasks(batchSize int) []Worker {
	taskLength := len(s.tasks)
	if taskLength == 0 {
		return nil
	}
	if batchSize > taskLength {
		batchSize = taskLength
	}
	return s.tasks[0:batchSize]
}

func (s *InMemoryStore) ClearPendingTasks(ctx context.Context, workers []Worker) {

	count := s.RemoveTasksByWorkers(ctx, workers)
	slog.Debug("removed tasks", "count", count)
}

// RemoveTasksByWorkers removes all tasks where the worker ID matches any of the provided workers
func (s *InMemoryStore) RemoveTasksByWorkers(ctx context.Context, workers []Worker) int {
	if len(workers) == 0 {
		slog.Debug("no tasks to remove")
		return 0
	}

	// Create a map for faster lookups
	idMap := make(map[string]struct{}, len(workers))
	for _, worker := range workers {
		idMap[worker.ID()] = struct{}{}
	}

	// Create a new slice to hold the tasks we're keeping
	newTasks := make([]Worker, 0, len(s.tasks))
	removedCount := 0

	// Only keep tasks whose worker ID is not in the map
	for _, task := range s.tasks {
		if _, exists := idMap[task.ID()]; !exists {
			newTasks = append(newTasks, task)
		} else {
			removedCount++
		}
	}

	// Replace the tasks slice with our filtered version
	s.tasks = newTasks

	return removedCount
}

func (s *InMemoryStore) Save(ctx context.Context, workers []Worker) error {
	if len(s.tasks) > s.maxTasks {
		slog.Debug("max tasks reached, adding 30% as failing tasks, discarding the rest")

		tasksAdded := false
		for _, worker := range workers {
			// Use random to determine if this task should be added as failing (30% chance)
			if rand.Float64() <= 0.3 {
				s.tasks = append(s.tasks, worker)
				tasksAdded = true
			}
		}

		// Notify if we added any tasks
		if tasksAdded {
			s.NotifyTasksAvailable()
		}

		return fmt.Errorf("max tasks reached: %d, added some tasks with 30%% probability", s.maxTasks)
	}
	tasksAdded := false
	for _, worker := range workers {
		s.tasks = append(s.tasks, worker)
		tasksAdded = true
	}

	// Notify only if tasks were actually added
	if tasksAdded {
		s.NotifyTasksAvailable()
	}

	return nil
}

func (s *InMemoryStore) Start(ctx context.Context) error {
	s.StartNotifier(ctx)
	return nil
}

// NotifyTasksAvailable sends a non-blocking notification that tasks are available
func (s *InMemoryStore) NotifyTasksAvailable() {
	// Non-blocking send to notification channel
	select {
	case s.notifications <- struct{}{}:
		// Successfully sent notification
		slog.Debug("Notified that tasks are available")
	default:
		// Channel already has a notification, no need to send another
	}
}

// GetNotificationChannel returns the channel that receives notifications when tasks are available
func (s *InMemoryStore) ReadyForTasksAvailable() <-chan struct{} {
	return s.notifications
}

// StartNotifier starts a background process that periodically checks for tasks and sends notifications
func (s *InMemoryStore) StartNotifier(ctx context.Context) {
	go func() {

		ticker := time.NewTicker(s.notificationPeriod)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				if len(s.tasks) >= s.batchSize {
					slog.Debug("periodic check found persisted tasks", "count", len(s.tasks))
					s.NotifyTasksAvailable()
				}
			}
		}
	}()
}
