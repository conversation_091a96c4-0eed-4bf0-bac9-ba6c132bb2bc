package workers

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"sync/atomic"
	"time"

	"github.com/failsafe-go/failsafe-go"
	"github.com/failsafe-go/failsafe-go/retrypolicy"
)

type WorkerPool struct {
	poolCapacity             int
	worker<PERSON>han               chan Worker
	wg                       sync.WaitGroup
	processedTasksCount      atomic.Int64
	submittedTasksCount      atomic.Int64
	deadLetter               chan Worker
	deadLetterTasksCount     atomic.Int64
	overflowQueue            chan Worker
	overflowBufferTasksCount atomic.Int64
	persistedMutex           sync.Mutex // Mutex for the persisted tasks list
	persistedTasksCount      atomic.Int64
	reQueueTasksCount        atomic.Int64 // Counter for reQueue tasks
	store                    StoreProcessor
	requeueRetryBuffer       []Worker // retry list for failed requeues
	requeueRetryBufferMutex  sync.Mutex
	failedTasksCount         atomic.Int64
	successfulTasksCount     atomic.Int64
	requeueRetryMutex        sync.Mutex
}

func NewWorkerPool(poolCapacity int) *WorkerPool {
	store := NewInMemoryStore()
	return &WorkerPool{
		poolCapacity:  poolCapacity,
		workerChan:    make(chan Worker, poolCapacity),
		deadLetter:    make(chan Worker),
		overflowQueue: make(chan Worker, poolCapacity*2), // Buffer with double capacity
		store:         store,
	}
}

func (wp *WorkerPool) Start(ctx context.Context, numWorkers int) error {
	for i := 0; i < numWorkers; i++ {
		wp.wg.Add(1)
		go func(id int) {
			defer wp.wg.Done()
			for {
				select {
				case worker, ok := <-wp.workerChan:
					if !ok {
						return
					}
					err := worker.Work(ctx)
					wp.processedTasksCount.Add(1)
					if err == nil {
						wp.successfulTasksCount.Add(1)
					}
					if err != nil {
						slog.Debug("worker failed", "worker_id", id, "error", err)
						wp.failedTasksCount.Add(1)
						// Add failed worker to dead letter queue
						select {
						case wp.deadLetter <- worker:
							wp.deadLetterTasksCount.Add(1)
							slog.Debug("worker added to dead letter queue", "worker_id", id)
						default:
							slog.Debug("dead letter queue is full, worker discarded", "worker_id", id)
						}
					}

				case <-ctx.Done():
					slog.Debug("worker pool context cancelled", "worker_id", id)
					return
				}
			}
		}(i)
	}
	wp.StartProcessors(ctx)
	return nil
}

func (wp *WorkerPool) PendingTasksCount() int {
	workers := wp.store.PendingTasks(wp.poolCapacity)
	return int(len(workers))
}

func (wp *WorkerPool) SuccessfulTasksCount() int {
	return int(wp.successfulTasksCount.Load())
}

func (wp *WorkerPool) FailedTasksCount() int {
	return int(wp.failedTasksCount.Load())
}

func (wp *WorkerPool) SubmittedTasksCount() int {
	return int(wp.submittedTasksCount.Load())
}

func (wp *WorkerPool) ProcessedTasksCount() int { // Add a method to get the total number of tasks processed
	return int(wp.processedTasksCount.Load())
}

func (wp *WorkerPool) DeadLetterCount() int {
	return int(wp.deadLetterTasksCount.Load())
}

func (wp *WorkerPool) PersistedTasksCount() int {
	return int(wp.persistedTasksCount.Load())
}

func (wp *WorkerPool) RequeueTasksCount() int {
	return int(wp.reQueueTasksCount.Load())
}

func (wp *WorkerPool) ProcessPersistedWorkers(ctx context.Context) error {
	slog.Debug("processing persisted workers")

	wp.persistedMutex.Lock()
	store := wp.store // Get reference to store under mutex
	workers := store.PendingTasks(wp.poolCapacity)
	persistedCount := len(workers)
	store.ClearPendingTasks(ctx, workers)
	wp.persistedTasksCount.Store(0)
	wp.persistedMutex.Unlock()

	slog.Debug("found persisted workers to reprocess", "count", persistedCount)

	for _, worker := range workers {
		select {
		case <-ctx.Done():
			slog.Debug("context cancelled, stopping processing of persisted workers")
			return ctx.Err()
		default:
			wp.Requeue(worker)
			time.Sleep(10 * time.Millisecond)
		}
	}

	return nil
}

func (wp *WorkerPool) Submit(worker Worker, withRetry bool) error {
	// Increment the submitted tasks counter.
	wp.submittedTasksCount.Add(1)

	// Try to send the worker directly to the main worker channel.
	select {
	case wp.workerChan <- worker:
		slog.Debug("worker directly enqueued to worker pool")
		return nil
	default:
		// If main channel is full, try the overflow queue.
		select {
		case wp.overflowQueue <- worker:
			slog.Debug("worker enqueued to overflow queue")
			return nil
		default:
			// Both channels are full.
			slog.Debug("both main and overflow queues are full, persisting worker")
			var err error
			if withRetry {
				err = wp.persistWorkerWithRetry(worker)
			} else {
				err = wp.persistWorker(worker)
			}
			if err != nil {
				return fmt.Errorf("failed to persist worker: %w", err)
			}
			// Update persisted counter.
			wp.persistedTasksCount.Add(1)
			return nil
		}
	}
}

func (wp *WorkerPool) RequeueRetryTasks() []Worker {
	wp.requeueRetryMutex.Lock()
	defer wp.requeueRetryMutex.Unlock()
	return wp.requeueRetryBuffer
}

func (wp *WorkerPool) RequeueRetryCount() int {
	wp.requeueRetryMutex.Lock()
	defer wp.requeueRetryMutex.Unlock()
	return len(wp.requeueRetryBuffer)
}

func (wp *WorkerPool) retryFailedRequeues(ctx context.Context) {
	slog.Debug("starting requeue retry processor")

	baseDelay := 500 * time.Millisecond
	maxDelay := 10 * time.Second
	delay := baseDelay

	for {
		select {
		case <-ctx.Done():
			slog.Debug("stopping requeue retry processor")
			return

		default:
			wp.requeueRetryBufferMutex.Lock()
			retryList := wp.requeueRetryBuffer
			wp.requeueRetryBuffer = nil // reset buffer
			wp.requeueRetryBufferMutex.Unlock()

			if len(retryList) > 0 {
				slog.Debug("retrying failed requeues", "count", len(retryList))
			}

			reEnqueued := 0
			for _, worker := range retryList {
				select {
				case wp.workerChan <- worker:
					reEnqueued++
				default:
					select {
					case wp.overflowQueue <- worker:
						wp.overflowBufferTasksCount.Add(1)
						reEnqueued++
					default:
						// Still full, re-append to retry list
						wp.requeueRetryBufferMutex.Lock()
						wp.requeueRetryBuffer = append(wp.requeueRetryBuffer, worker)
						wp.requeueRetryBufferMutex.Unlock()
					}
				}
			}

			if reEnqueued > 0 {
				slog.Debug("successfully re-enqueued workers from retry buffer", "count", reEnqueued)
			}

			// Increase delay only if everything failed again
			if reEnqueued == 0 {
				delay *= 2
				if delay > maxDelay {
					delay = maxDelay
				}
			} else {
				delay = baseDelay
			}

			time.Sleep(delay)
		}
	}
}

func (wp *WorkerPool) Requeue(worker Worker) {
	// Always increment the requeue count for any requeued task
	wp.reQueueTasksCount.Add(1)

	select {
	case wp.workerChan <- worker:
		slog.Debug("requeue worker added to worker pool")

	default:
		select {
		case wp.overflowQueue <- worker:
			wp.overflowBufferTasksCount.Add(1)
			slog.Debug("worker pool is full, requeue worker added to overflow queue")

		default:
			// Could not enqueue — keep for retry
			slog.Debug("worker pool and overflow full, requeue worker added to retry buffer")

			wp.requeueRetryBufferMutex.Lock()
			wp.requeueRetryBuffer = append(wp.requeueRetryBuffer, worker)
			wp.requeueRetryBufferMutex.Unlock()
		}
	}
}

func (wp *WorkerPool) Stop(ctx context.Context) error {
	slog.Debug("stopping worker pool")
	done := make(chan struct{})
	go func() {
		// Process any remaining workers in the overflow queue before shutting down
		wp.drainOverflowQueue(ctx)

		// Try to process persisted workers
		if err := wp.ProcessPersistedWorkers(ctx); err != nil {
			slog.Error("failed to process all persisted workers during shutdown", "error", err)
		}
		close(wp.workerChan)
		wp.wg.Wait()

		close(wp.deadLetter)
		close(wp.overflowQueue)
		slog.Debug("worker pool channels closed")

		close(done)
	}()

	select {
	case <-ctx.Done():
		slog.Debug("worker pool context cancelled")
	case <-done:
		slog.Debug("worker pool stopped")
	}
	return nil
}

func (wp *WorkerPool) drainOverflowQueue(ctx context.Context) {
	slog.Debug("draining overflow queue")

	timeout := time.After(3 * time.Second) // Set a timeout for draining

	for {
		select {
		case <-timeout:
			slog.Debug("overflow queue drain timeout reached")
			return
		case <-ctx.Done():
			slog.Debug("overflow queue drain cancelled")
			return
		case worker, ok := <-wp.overflowQueue:
			if !ok {
				return
			}

			select {
			case wp.workerChan <- worker:
				slog.Debug("worker from overflow queue moved to main queue during shutdown")

			default:
				// If main queue is full, add back to overflow queue and try again
				select {
				case wp.overflowQueue <- worker:
					slog.Debug("worker returned to overflow queue during shutdown")
					wp.overflowBufferTasksCount.Add(1)
				default:
					slog.Debug("failed to return worker to overflow queue during shutdown, worker lost")
				}
				time.Sleep(50 * time.Millisecond)
			}
		default:
			slog.Debug("overflow queue drained")
			return
		}
	}
}

func (wp *WorkerPool) ProcessOverflowQueue(ctx context.Context) {
	slog.Debug("processing overflow queue")
	wp.wg.Add(1)
	// Start a goroutine to continuously process the overflow queue
	go func() {
		defer wp.wg.Done()
		for {
			select {
			case <-ctx.Done():
				slog.Debug("Overflow queue processor stopped due to context cancellation")
				return
			case worker, ok := <-wp.overflowQueue:
				if !ok {
					slog.Debug("Overflow queue channel closed, stopping processor")
					return
				}

				// Try to submit to main queue
				select {
				case wp.workerChan <- worker:
					slog.Debug("Worker from overflow queue submitted to worker pool")

				case <-ctx.Done():
					// Put the worker back in the overflow queue
					select {
					case wp.overflowQueue <- worker:
						slog.Debug("Worker returned to overflow queue due to context cancellation")
						wp.overflowBufferTasksCount.Add(1)
					default:
						slog.Debug("Failed to return worker to overflow queue, worker lost")
					}
					return
				default:
					// Main queue still full, put the worker back in the overflow queue
					select {
					case wp.overflowQueue <- worker:
						slog.Debug("Main queue still full, worker returned to overflow queue")
					default:
						slog.Debug("Failed to return worker to overflow queue, worker lost")
					}
					// Wait a bit before trying again
					time.Sleep(100 * time.Millisecond)
				}
			default:
				// No workers in overflow queue, wait a bit
				time.Sleep(100 * time.Millisecond)
			}
		}
	}()
}

func (wp *WorkerPool) ProcessDeadLetterQueue(ctx context.Context) error {
	slog.Debug("Processing dead letter queue...")

	// Create a temporary channel to hold workers from the dead letter queue
	tempQueue := make([]Worker, 0, wp.DeadLetterCount())

	// Drain the dead letter queue
	for {
		select {
		case worker, ok := <-wp.deadLetter:
			if !ok {
				break
			}
			tempQueue = append(tempQueue, worker)
		default:
			// No more workers in the dead letter queue
			goto process
		}
	}

process:
	slog.Debug("found workers in dead letter queue", "count", len(tempQueue))

	// Resubmit workers to the worker pool
	for _, worker := range tempQueue {
		wp.Requeue(worker) // Use Requeue instead of Submit
	}

	// Reset the dead letter count
	wp.deadLetterTasksCount.Store(0)

	return nil
}

func (wp *WorkerPool) OverflowBufferCount() int {

	return int(wp.overflowBufferTasksCount.Load())
}

func (wp *WorkerPool) persistWorker(worker Worker) error {
	wp.persistedMutex.Lock()
	defer wp.persistedMutex.Unlock()

	return wp.store.Save(context.Background(), []Worker{worker})
}

func (wp *WorkerPool) persistWorkerWithRetry(worker Worker) error {
	// Retry  up to 3 times with a 1 second delay between attempts
	retryPolicy := retrypolicy.Builder[Worker]().
		WithBackoff(time.Second, 30*time.Second).
		WithMaxRetries(5).
		WithMaxDuration(1 * time.Minute).
		Build()

	retryCallback := func() (Worker, error) {
		err := wp.persistWorker(worker)
		if err != nil {
			slog.Debug("failed to persist worker, will retry", "error", err)
			return worker, err
		}
		return worker, nil
	}

	// Get with retries
	_, err := failsafe.Get(retryCallback, retryPolicy)

	if err != nil {
		slog.Debug("failed to persist worker after retries", "error", err)
		return err
	}

	return err
}

func (wp *WorkerPool) StartProcessors(ctx context.Context) error {
	// Start the overflow queue processor
	wp.ProcessOverflowQueue(ctx)
	err := wp.ProcessDeadLetterQueue(ctx)
	if err != nil {
		return fmt.Errorf("failed to process dead letter queue: %v", err)
	}

	err = wp.store.Start(ctx)
	if err != nil {
		return fmt.Errorf("failed to start store: %v", err)
	}
	wp.wg.Add(1)
	// Process persisted workers when notified
	go func() {
		defer wp.wg.Done()
		notificationChan := wp.store.ReadyForTasksAvailable()
		for {
			select {
			case <-ctx.Done():
				return
			case <-notificationChan:
				if wp.PersistedTasksCount() > 0 {
					slog.Debug("notification received: processing persisted workers", "count", wp.PersistedTasksCount())
					if err := wp.ProcessPersistedWorkers(ctx); err != nil {
						slog.Error("failed to process persisted workers", "error", err)
					}
				}
			}
		}
	}()

	wp.wg.Add(1)
	go func() {
		defer wp.wg.Done()
		wp.retryFailedRequeues(ctx)
	}()

	return nil
}
