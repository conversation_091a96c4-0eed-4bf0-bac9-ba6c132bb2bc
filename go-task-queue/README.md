#  Worker pool

# Run

```
go run main.go
```


## Test

```
go test -v -count=1 ./workers -run TestWorkerPoolWithManyTasks
```

## Benchmark

```
go test -bench=BenchmarkWorkerPool ./workers

```

## Run Manual Test

```
for i in {1..2000}; do curl -s "http://localhost:8080/submit?text=job$i" & done
```

## Result

```bash
2025/04/12 11:47:55 Total jobs submitted: 2154
2025/04/12 11:47:55 Jobs processed: 2000
2025/04/12 11:47:55 Jobs in dead letter queue: 0
2025/04/12 11:47:55 Jobs remaining in buffer: 0
2025/04/12 11:47:55 Jobs dropped: 0
2025/04/12 11:47:55 Jobs persisted in memory: 0
2025/04/12 11:47:55 Jobs resubmitted: 154
2025/04/12 11:47:55 Original jobs count: 2000
2025/04/12 11:47:55 Server and worker pool shutdown complete
```

## Test

```
go test -v ./workers -run TestWorkerPoolWithManyTasks
```