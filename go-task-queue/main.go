package main

import (
	"context"
	"demo/workers"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"runtime"
	"sync/atomic"
	"syscall"
	"time"
)

var totalTasksSubmitted atomic.Int64

type Worker struct {
	workerName string
	workerID   int
	value      string
}

func (ejt *Worker) ID() string {
	return fmt.Sprintf("%s-%d", ejt.workerName, ejt.workerID)
}

var _ workers.Worker = (*Worker)(nil)

func (ejt *Worker) Work(ctx context.Context) error {
	workerCtx, cancel := context.WithTimeout(ctx, 15*time.Second)
	defer cancel()
	totalTasksSubmitted.Add(1)
	select {
	case <-ctx.Done():
		slog.Debug("Task cancelled")
		return ctx.Err()
	case <-workerCtx.Done():
		slog.Debug("Task timed out")
		return workerCtx.Err()
	default:
		slog.Debug("Task started", "id", ejt.workerID)
		time.Sleep(1000 * time.Millisecond) // Simulate working
		slog.Debug("Task completed", "id", ejt.workerID)
		slog.Debug("Task value", "id", ejt.workerID, "value", ejt.value)
		return nil
	}
}

func NewWorker(workerName string, workerID int, value string) *Worker {
	return &Worker{
		workerName: workerName,
		workerID:   workerID,
		value:      value,
	}
}

func main() {
	opts := &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}
	handler := slog.NewTextHandler(os.Stdout, opts)
	logger := slog.New(handler)
	slog.SetDefault(logger)
	if err := run(); err != nil {
		slog.Error("application failed", "error", err)
		os.Exit(1)
	}
}

func run() error {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// number of concurrent workers
	numWorkers := runtime.NumCPU() * 10
	// capacity of the worker pool
	pool := numWorkers * 2

	workerPool := workers.NewWorkerPool(pool)

	err := workerPool.Start(ctx, numWorkers)

	if err != nil {
		slog.Error("failed to start worker pool", "error", err)
		return err
	}

	mux := http.NewServeMux()
	mux.HandleFunc("/submit", func(w http.ResponseWriter, r *http.Request) {
		text := r.URL.Query().Get("text")
		if text == "" {
			http.Error(w, "text parameter is required", http.StatusBadRequest)
			return
		}
		slog.Debug("Received worker", "text", text)

		worker := NewWorker("example", 1, text)

		erro := workerPool.Submit(worker, false)

		if erro != nil {
			slog.Error("Failed to submit worker", "error", erro)
			http.Error(w, "Failed to submit worker", http.StatusInternalServerError)
			return
		}

		w.WriteHeader(http.StatusOK)
		w.Write([]byte("Task submitted"))
	})

	server := &http.Server{
		Addr:    ":8080",
		Handler: mux,
	}

	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			slog.Error("ListenAndServe failed", "error", err)
			os.Exit(1)
		}
	}()

	// Wait for a signal to stop the server
	stop := make(chan os.Signal, 1)
	signal.Notify(stop, syscall.SIGINT, syscall.SIGTERM)

	<-stop
	slog.Debug("Received shutdown signal, shutting down server...")

	ctxShutdown, shutdownCancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer shutdownCancel()

	if err := server.Shutdown(ctxShutdown); err != nil {
		slog.Error("Server shutdown failed", "error", err)
	}

	ctxPoolCancel, poolCancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer poolCancel()

	if err := workerPool.Stop(ctxPoolCancel); err != nil {
		slog.Error("Worker pool shutdown failed", "error", err)
	}

	slog.Info("Worker pool shutdown complete",
		"tasksProcessed", workerPool.ProcessedTasksCount())
	slog.Info("Tasks statistics",
		"submitted", workerPool.SubmittedTasksCount(),
		"processed", workerPool.ProcessedTasksCount(),
		"deadLetter", workerPool.DeadLetterCount(),
		"overflowBuffer", workerPool.OverflowBufferCount(),
		"failed", workerPool.FailedTasksCount())
	slog.Info("More task statistics",
		"successful", workerPool.SuccessfulTasksCount(),
		"persisted", workerPool.PersistedTasksCount(),
		"requeued", workerPool.RequeueTasksCount(),
		"pendingRequeues", len(workerPool.RequeueRetryTasks()))
	slog.Info("Server and worker pool shutdown complete")
	slog.Info("Total tasks submitted", "count", totalTasksSubmitted.Load())

	return nil
}
