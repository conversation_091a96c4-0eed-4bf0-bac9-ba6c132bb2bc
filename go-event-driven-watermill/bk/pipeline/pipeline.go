package pipeline

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/ThreeDotsLabs/watermill/message/router/middleware"
	"github.com/ThreeDotsLabs/watermill/message/router/plugin"
	"github.com/ThreeDotsLabs/watermill/pubsub/gochannel"
)

var (
	logger = watermill.NewStdLogger(false, false)
)

type Pipeline struct {
	Router *message.Router
	pub    message.Publisher
	sub    message.Subscriber
	pubWG  sync.WaitGroup
}

func (p *Pipeline) WaitForPublishers(ctx context.Context) {
	p.pubWG.Wait()
}

func (p *Pipeline) Run(ctx context.Context) {
	// ctx, cancel := context.WithCancel(ctx)

	// Run the router in a separate goroutine
	go func() {
		if err := p.Router.Run(ctx); err != nil {
			panic(err)
		}
	}()

	p.PublishMessagesAsync()
	p.WaitForPublishers(ctx)

	// Wait for the router to stop
	<-p.Router.Running()
	// cancel()
}

func NewPipeline() *Pipeline {
	router, err := message.NewRouter(message.RouterConfig{
		CloseTimeout: time.Second * 10,
	}, logger)
	if err != nil {
		panic(err)
	}

	router.AddPlugin(plugin.SignalsHandler)

	router.AddMiddleware(
		middleware.CorrelationID,
		middleware.Retry{
			MaxRetries:      3,
			InitialInterval: time.Millisecond * 100,
			Logger:          logger,
		}.Middleware,
		middleware.Recoverer,
	)

	pubSub := gochannel.NewGoChannel(gochannel.Config{}, logger)

	// handler := router.AddHandler(
	// 	"struct_handler",
	// 	"incoming_messages_topic",
	// 	pubSub,
	// 	"outgoing_messages_topic",
	// 	pubSub,
	// 	structHandler{
	// 		Router: router,
	// 	}.Handler,
	// )

	// handler.AddMiddleware(func(h message.HandlerFunc) message.HandlerFunc {
	// 	return func(message *message.Message) ([]*message.Message, error) {
	// 		log.Println("executing handler specific middleware for ", message.UUID)
	// 		return h(message)
	// 	}
	// })

	router.AddNoPublisherHandler(
		"print_incoming_messages",
		"incoming_messages_topic",
		pubSub,
		printMessages,
	)

	router.AddNoPublisherHandler(
		"print_outgoing_messages",
		"outgoing_messages_topic",
		pubSub,
		printMessages,
	)

	p := &Pipeline{
		Router: router,
		pub:    pubSub,
		sub:    pubSub,
	}

	return p
}

func (p *Pipeline) AddHandler(handlerName string, subscribeTopic string, publishTopic string, handleFunc message.HandlerFunc) *message.Handler {
	handler := p.Router.AddHandler(
		handlerName,
		subscribeTopic,
		p.sub,
		publishTopic,
		p.pub,
		handleFunc,
	)

	handler.AddMiddleware(func(h message.HandlerFunc) message.HandlerFunc {
		return func(message *message.Message) ([]*message.Message, error) {
			log.Println("executing handler specific middleware for ", message.UUID)
			return h(message)
		}
	})

	return handler
}

func (p *Pipeline) publishMessages() {
	for i := 0; i < 10; i++ {
		msgTxt := fmt.Sprintf("Hello, world! %d", i)
		msg := message.NewMessage(watermill.NewUUID(), []byte(msgTxt))
		middleware.SetCorrelationID(watermill.NewUUID(), msg)

		log.Printf("sending message %s, correlation id: %s\n", msg.UUID, middleware.MessageCorrelationID(msg))

		if err := p.pub.Publish("incoming_messages_topic", msg); err != nil {
			panic(err)
		}

		time.Sleep(time.Second)
	}
}

// Add a new method to publish messages
func (p *Pipeline) PublishMessages() {
	p.publishMessages()
}
func (p *Pipeline) PublishMessagesAsync() {
	p.pubWG.Add(1)
	go func() {
		defer p.pubWG.Done()
		p.PublishMessages()
	}()
}

func printMessages(msg *message.Message) error {
	fmt.Printf(
		"\n> Received message: %s\n> %s\n> metadata: %v\n\n",
		msg.UUID, string(msg.Payload), msg.Metadata,
	)
	return nil
}

type Step1Sub struct {
	Router *message.Router
}

func (s Step1Sub) Handler(msg *message.Message) ([]*message.Message, error) {
	log.Println("----------- Received message Handler -----------", msg.UUID)
	log.Println("structHandler received message", string(msg.Payload))

	msg = message.NewMessage(watermill.NewUUID(), []byte("message produced by structHandler"))
	return message.Messages{msg}, nil
}
