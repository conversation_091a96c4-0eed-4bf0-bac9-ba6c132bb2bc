package main

import (
	"context"
	"demo/pipeline"
	"log"
)

func main() {
	ctx := context.Background()
	pip, err := pipeline.NewPipeline()
	if err != nil {
		panic(err)
	}
	step1Pub := pipeline.Step1Publisher{}
	step1Handler := pipeline.Step1Sub{}
	step2Handler := pipeline.FinalStepSub{}
	pip.AddPublisher(step1Pub)
	pip.AddHandler(pipeline.HandlerConfig{
		Name:        "step1sub",
		InTopic:     "publisher_inc_messages",
		OutTopic:    "step_2_inc_messages",
		HandlerFunc: step1Handler.Handler,
	})
	pip.AddNoPubHandler(pipeline.NoPubHandlerConfig{
		Name:        "step2sub",
		InTopic:     "step_2_inc_messages",
		HandlerFunc: step2Handler.Handler,
	})
	pip.Run(ctx)

	log.Println("Pipeline finished, continuing execution.")
	log.Println("\n\n Pipeline results: \n\n", step2Handler.Results())
	// Add any further logic here
}
