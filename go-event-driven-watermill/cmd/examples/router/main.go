package main

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/ThreeDotsLabs/watermill/message/router/middleware"
	"github.com/ThreeDotsLabs/watermill/message/router/plugin"
	"github.com/ThreeDotsLabs/watermill/pubsub/gochannel"
)

type App struct {
	logger   watermill.LoggerAdapter
	pubSub   *gochannel.GoChannel
	wg       sync.WaitGroup
	stopChan chan struct{}
}

func NewApp() *App {
	return &App{
		logger:   watermill.NewStdLogger(false, false),
		pubSub:   gochannel.NewGoChannel(gochannel.Config{}, watermill.NewStdLogger(false, false)),
		stopChan: make(chan struct{}),
	}
}

func (app *App) Run() error {
	router, err := message.NewRouter(message.RouterConfig{}, app.logger)
	if err != nil {
		return err
	}

	router.AddPlugin(plugin.SignalsHandler)

	router.AddMiddleware(
		middleware.CorrelationID,
		middleware.Retry{
			MaxRetries:      3,
			InitialInterval: time.Millisecond * 100,
			Logger:          app.logger,
		}.Middleware,
		middleware.Recoverer,
	)

	handler := router.AddHandler(
		"struct_handler",
		"incoming_messages_topic",
		app.pubSub,
		"outgoing_messages_topic",
		app.pubSub,
		app.structHandler,
	)

	handler.AddMiddleware(func(h message.HandlerFunc) message.HandlerFunc {
		return func(msg *message.Message) ([]*message.Message, error) {
			app.wg.Add(1)
			defer app.wg.Done()
			log.Println("executing handler specific middleware for ", msg.UUID)
			return h(msg)
		}
	})

	router.AddNoPublisherHandler(
		"print_incoming_messages",
		"incoming_messages_topic",
		app.pubSub,
		app.printMessages,
	)

	router.AddNoPublisherHandler(
		"print_outgoing_messages",
		"outgoing_messages_topic",
		app.pubSub,
		app.printMessages,
	)

	go app.publishMessages()

	go func() {
		app.wg.Wait()
		close(app.stopChan)
		log.Println("All messages processed, closing router...")
		router.Close()
	}()

	ctx := context.Background()
	if err := router.Run(ctx); err != nil && err != context.Canceled {
		return err
	}

	<-app.stopChan
	log.Println("All messages processed, continuing execution...")
	return nil
}

func (app *App) publishMessages() {
	for i := 0; i < 10; i++ {
		app.wg.Add(1)
		defer app.wg.Done()
		msg := message.NewMessage(watermill.NewUUID(), []byte("Hello, world!"))
		middleware.SetCorrelationID(watermill.NewUUID(), msg)

		log.Printf("sending message %s, correlation id: %s\n", msg.UUID, middleware.MessageCorrelationID(msg))

		if err := app.pubSub.Publish("incoming_messages_topic", msg); err != nil {
			panic(err)
		}

		time.Sleep(time.Second)
	}
}

func (app *App) structHandler(msg *message.Message) ([]*message.Message, error) {
	log.Println("structHandler received message", msg.UUID)
	msg = message.NewMessage(watermill.NewUUID(), []byte("message produced by structHandler"))
	return message.Messages{msg}, nil
}

func (app *App) printMessages(msg *message.Message) error {
	fmt.Printf(
		"\n> Received message: %s\n> %s\n> metadata: %v\n\n",
		msg.UUID, string(msg.Payload), msg.Metadata,
	)
	return nil
}

func main() {
	app := NewApp()
	if err := app.Run(); err != nil {
		log.Fatal(err)
	}
}
