package pipeline

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/ThreeDotsLabs/watermill/message/router/middleware"
	"github.com/ThreeDotsLabs/watermill/message/router/plugin"
	"github.com/ThreeDotsLabs/watermill/pubsub/gochannel"
)

var (
	logger = watermill.NewStdLogger(false, false)
)

type HandlerConfig struct {
	Name        string
	InTopic     string
	OutTopic    string
	HandlerFunc message.HandlerFunc
}

type NoPubHandlerConfig struct {
	Name        string
	InTopic     string
	HandlerFunc message.NoPublishHandlerFunc
}

type Publisher interface {
	Publish(ctx context.Context, pub message.Publisher) error
}

type SubscriberPublisher interface {
	message.HandlerFunc
}

type Subscriber interface {
	message.NoPublishHandlerFunc
}

type options struct {
	pub message.Publisher
	sub message.Subscriber
}

type Option interface {
	apply(*options)
}

type Pipeline struct {
	router     *message.Router
	pub        message.Publisher
	sub        message.Subscriber
	pubWg      sync.WaitGroup
	subWg      sync.WaitGroup
	stopChan   chan struct{}
	publishers []Publisher
}

func (p *Pipeline) WaitForPublishers(ctx context.Context) {
	p.pubWg.Wait()
}

func (p *Pipeline) WaitForSubscribers(ctx context.Context) {
	p.pubWg.Wait()
}

func (p *Pipeline) Router() *message.Router {
	return p.router
}

func (p *Pipeline) Run(ctx context.Context) error {

	go p.RunPublishers(ctx)

	go p.WaitAndCleanup(ctx)

	if err := p.router.Run(ctx); err != nil && err != context.Canceled {
		return err
	}

	<-p.stopChan

	return nil
}

// WaitUntilDone coordinates the graceful shutdown sequence by waiting for the router
// to be ready, ensuring all publishers and subscribers have completed their work,
// and performing final cleanup by closing channels and shutting down the router.
func (p *Pipeline) WaitAndCleanup(ctx context.Context) {
	p.WaitForRouter()
	p.WaitForPublishers(ctx)
	p.WaitForSubscribers(ctx)
	close(p.stopChan)
	log.Println("All messages processed, closing router...")
	p.router.Close()
}

func (p *Pipeline) WaitForRouter() {
	<-p.router.Running()
}

func NewPipeline(opts ...Option) (*Pipeline, error) {

	options := &options{}
	for _, opt := range opts {
		opt.apply(options)
	}

	if options.pub == nil && options.sub == nil {
		pubSub := gochannel.NewGoChannel(gochannel.Config{}, logger)
		options.pub = pubSub
		options.sub = pubSub
	}

	if options.pub == nil {
		return nil, fmt.Errorf("pub is required")
	}

	if options.sub == nil {
		return nil, fmt.Errorf("sub is required")
	}

	router, err := message.NewRouter(message.RouterConfig{
		CloseTimeout: time.Second * 5,
	}, logger)

	if err != nil {
		return nil, err
	}

	router.AddPlugin(plugin.SignalsHandler)

	router.AddMiddleware(
		middleware.CorrelationID,
		middleware.Retry{
			MaxRetries:      3,
			InitialInterval: time.Millisecond * 100,
			Logger:          logger,
		}.Middleware,
		middleware.Recoverer,
	)

	p := &Pipeline{
		router:   router,
		pub:      options.pub,
		sub:      options.sub,
		stopChan: make(chan struct{}),
	}

	return p, nil
}

func (p *Pipeline) AddPublisher(pub Publisher) {
	p.publishers = append(p.publishers, pub)
}

func (p *Pipeline) AddHandler(config HandlerConfig) *message.Handler {
	handler := p.router.AddHandler(
		config.Name,
		config.InTopic,
		p.sub,
		config.OutTopic,
		p.pub,
		config.HandlerFunc,
	)

	handler.AddMiddleware(func(h message.HandlerFunc) message.HandlerFunc {
		return func(msg *message.Message) ([]*message.Message, error) {
			p.subWg.Add(1)
			defer p.subWg.Done()
			log.Println("executing handler specific middleware for ", msg.UUID)
			return h(msg)
		}
	})

	return handler
}

func (p *Pipeline) AddNoPubHandler(config NoPubHandlerConfig) *message.Handler {
	handler := p.router.AddNoPublisherHandler(
		config.Name,
		config.InTopic,
		p.sub,
		config.HandlerFunc,
	)

	handler.AddMiddleware(func(h message.HandlerFunc) message.HandlerFunc {
		return func(msg *message.Message) ([]*message.Message, error) {
			p.subWg.Add(1)
			defer p.subWg.Done()
			log.Println("executing no pub handler specific middleware for ", msg.UUID)
			return h(msg)
		}
	})

	return handler
}

func (p *Pipeline) RunPublishers(ctx context.Context) {
	for _, pub := range p.publishers {
		p.pubWg.Add(1)
		go func(pub Publisher) {
			defer p.pubWg.Done()
			if err := pub.Publish(ctx, p.pub); err != nil {
				panic(err)
			}
		}(pub)
	}
}
