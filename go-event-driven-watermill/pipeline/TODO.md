# Improvements:

// Creation
// NewPipeline creates a new pipeline instance with the given configuration options
pipeline := NewPipeline(options...)

// Stream definition
// Source defines an input source that produces data into the pipeline
pipeline.Source("source-name", sourceConfig)
// Transform defines a transformation step that processes data
pipeline.Transform("transform-name", transformConfig)
// Sink defines an output destination that consumes the final data
pipeline.Sink("sink-name", sinkConfig)

// Event handling
// OnEvent registers a handler for specific named events in the pipeline
pipeline.OnEvent(eventName, handlerFunc)
// OnError registers a global error handler for the pipeline
pipeline.OnError(errorHandler)
// OnComplete registers a handler that executes when pipeline processing completes
pipeline.OnComplete(completionHandler)

// Flow control
// Start begins pipeline execution with the given context
pipeline.Start(ctx)
// Stop gracefully terminates pipeline execution
pipeline.Stop(ctx)
// Pause temporarily halts pipeline processing
pipeline.Pause()
// Resume continues pipeline processing after a pause
pipeline.Resume()

// Monitoring
// Status returns the current pipeline execution status
pipeline.Status() Status
// Metrics returns performance and operational metrics
pipeline.Metrics() Metrics
// Health returns the health status of the pipeline
pipeline.Health() Health

// Configuration
// WithRetry configures retry behavior for failed operations
pipeline.WithRetry(retryConfig)
// WithTimeout sets global timeout for pipeline operations
pipeline.WithTimeout(duration)
// WithBackpressure configures how pipeline handles overflow
pipeline.WithBackpressure(config)

// Batch operations
// Batch groups elements into batches of specified size
pipeline.Batch(size int)
// Window groups elements within specified time windows
pipeline.Window(duration time.Duration)

// Debugging
// Debug enables/disables debug logging
pipeline.Debug(enabled bool)
// Trace returns detailed execution trace information
pipeline.Trace() TraceInfo

// Composition & Chaining
// Chain connects this pipeline to another pipeline sequentially
pipeline.Chain(otherPipeline)
// Merge combines multiple pipelines into a single pipeline
pipeline.Merge(pipelines ...Pipeline)
// Split divides pipeline into multiple branches based on conditions
pipeline.Split(conditions ...SplitCondition)

// Filtering & Mapping
// Filter keeps elements that match the predicate
pipeline.Filter(predicate FilterFunc)
// Map transforms each element using the mapper function
pipeline.Map(mapper MapperFunc)
// FlatMap transforms and flattens elements using the mapper function
pipeline.FlatMap(mapper FlatMapperFunc)

// Backpressure & Flow Control
// Buffer creates a buffer of specified size for overflow handling
pipeline.Buffer(size int)
// ThrottleFirst limits throughput by taking first element in time window
pipeline.ThrottleFirst(duration time.Duration)
// ThrottleLast limits throughput by taking last element in time window
pipeline.ThrottleLast(duration time.Duration)

// Error Handling
// OnErrorResume continues pipeline execution with fallback handler on error
pipeline.OnErrorResume(handler ErrorHandlerFunc)
// OnErrorReturn continues pipeline execution with fallback value on error
pipeline.OnErrorReturn(fallbackValue interface{})
// DoOnError executes an action when error occurs without affecting pipeline
pipeline.DoOnError(action ErrorActionFunc)

// Lifecycle Hooks
// DoOnSubscribe executes when pipeline starts processing
pipeline.DoOnSubscribe(action SubscribeActionFunc)
// DoOnComplete executes when pipeline successfully completes
pipeline.DoOnComplete(action CompleteActionFunc)
// DoFinally executes when pipeline terminates (success or failure)
pipeline.DoFinally(action FinalActionFunc)

// Timing Operations
// Timeout sets maximum duration for pipeline execution
pipeline.Timeout(duration time.Duration)
// DelayElements adds delay between processing each element
pipeline.DelayElements(duration time.Duration)
// Sample takes periodic snapshots of pipeline data
pipeline.Sample(duration time.Duration)

// Utility Operations
// Cache stores pipeline results for reuse
pipeline.Cache()
// Distinct removes duplicate elements from pipeline
pipeline.Distinct()
// Take limits pipeline to first n elements
pipeline.Take(n int)
// Skip ignores first n elements in pipeline
pipeline.Skip(n int)
