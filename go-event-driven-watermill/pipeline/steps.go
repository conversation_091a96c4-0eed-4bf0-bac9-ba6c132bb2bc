package pipeline

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/ThreeDotsLabs/watermill/message/router/middleware"
)

type Step1Sub struct {
}

func (s Step1Sub) Handler(msg *message.Message) ([]*message.Message, error) {
	log.Println("----------- Received message Handler -----------", msg.UUID)
	log.Println("structHandler received message", string(msg.Payload))

	msg = message.NewMessage(watermill.NewUUID(), []byte("message produced by structHandler"))
	return message.Messages{msg}, nil
}

type Step1Publisher struct {
}

func (s Step1Publisher) Publish(ctx context.Context, pub message.Publisher) error {
	for i := 0; i < 10; i++ {
		msgTxt := fmt.Sprintf("Hello, world! %d", i)
		msg := message.NewMessage(watermill.NewUUID(), []byte(msgTxt))
		middleware.SetCorrelationID(watermill.NewUUID(), msg)

		log.Printf("sending message %s, correlation id: %s\n", msg.UUID, middleware.MessageCorrelationID(msg))

		if err := pub.Publish("publisher_inc_messages", msg); err != nil {
			panic(err)
		}

		time.Sleep(10 * time.Millisecond)
	}
	return nil
}

type FinalStepSub struct {
	results []string
}

func (s *FinalStepSub) Handler(msg *message.Message) error {
	log.Println("----------- Received message NO PUB Handler -----------", msg.UUID)
	log.Println("structHandler received message", string(msg.Payload))
	s.results = append(s.results, string(msg.Payload))
	return nil
}

func (s *FinalStepSub) Results() []string {
	return s.results
}
